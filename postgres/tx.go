package postgres

import (
	"context"
	"github.com/jackc/pgx/v4"
)

type Tx struct {
	otx pgx.Tx
}

func (tx Tx) Exec(sql string, args ...interface{}) (CmdTag, error) {
	otag, err := tx.otx.Exec(context.Background(), sql, args...)
	return CmdTag{otag: otag}, err
}

func (tx Tx) Get(query string, dst interface{}, args ...interface{}) error {
	return scan.Get(context.Background(), tx.otx, dst, query, args...)
}

func (tx Tx) Select(query string, dst interface{}, args ...interface{}) error {
	return scan.Select(context.Background(), tx.otx, dst, query, args...)
}
