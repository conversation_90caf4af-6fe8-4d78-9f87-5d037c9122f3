package handlers

import (
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/services/hosting"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
	"gitlab.insidebt.net/btfs/storage3-backend/utils"
)

var (
	ErrAddressInvalid  = errors.New("address invalid")
	ErrAddressNotFound = errors.New("address not found")
)

// TransferSite 发起站点转移
func TransferSite(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.TransferSiteArg{}
	err = ctx.ShouldBind(&arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	if time.Now().UTC().Sub(arg.TransferCreatedAt) < 0 {
		response.ParamErr(ctx, errors.New("invalid created_at"))
		return
	}

	if err = validateAddress(arg.TargetAddress, arg.TargetAddressType); err != nil {
		if errors.Is(err, ErrAddressInvalid) {
			response.BadRequestErr(ctx, response.CodeAddressInvalid, err)
			return
		}
		if errors.Is(err, ErrAddressNotFound) {
			response.BadRequestErr(ctx, response.CodeAddressNotExits, err)
			return
		}
		response.InternalErr(ctx)
		return
	}

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	err = validateSignature(user.Address, user.Source, arg.SignatureMessage, arg.Signature)
	if err != nil {
		response.BadRequestErr(ctx, response.CodeInvalidSignature, err)
		return
	}

	if model.UserSource(arg.TargetAddressType) == model.UserSourceTRON {
		arg.TargetAddress = tron.TronAddressToEtherAddress(arg.TargetAddress)
	}

	arg.TargetAddress = strings.ToLower(arg.TargetAddress)

	err = hosting.TransferSite(user, arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.OK(ctx)
}

func AcceptOrDeclineSite(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	arg := &model.TransferSiteOpArg{}
	err = ctx.ShouldBind(&arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}
	if arg.Operation == "ACCEPT" && (arg.Signature == "" || arg.SignatureMessage == "") {
		response.ParamErr(ctx, errors.New("invalid params"))
		return
	}
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	if arg.Operation == "ACCEPT" {
		err = validateSignature(user.Address, user.Source, arg.SignatureMessage, arg.Signature)
		if err != nil {
			response.BadRequestErr(ctx, response.CodeInvalidSignature, err)
			return
		}
	}

	err = hosting.AcceptOrDeclineSite(user, arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.OK(ctx)
}

func SiteTransferHistory(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	var arg model.SiteHistoryArg
	err = ctx.ShouldBind(&arg)
	// TODO delete no use fields
	history, err := hosting.GetSiteTransferHistory(arg.SiteId)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	for i := 0; i < len(history); i++ {
		if history[i].FromAddressType == model.UserSourceTRON {
			history[i].FromAddress = tron.HexAddressToBase58Address(history[i].FromAddress)
		}
		if history[i].ToAddressType == model.UserSourceTRON {
			history[i].ToAddress = tron.HexAddressToBase58Address(history[i].ToAddress)
		}
	}
	response.DataOK(ctx, response.Data{"history": history})
}

func validateAddress(targetAddress, targetAddressType string) error {
	addr := targetAddress
	if model.UserSource(targetAddressType) == model.UserSourceTRON {
		if len(targetAddress) != 34 {
			return ErrAddressInvalid
		}
		addr = tron.TronAddressToEtherAddress(targetAddress)
	}

	if model.UserSource(targetAddressType) == model.UserSourceBTTC {
		addr = strings.ToLower(targetAddress)
		if !strings.HasPrefix(addr, "0x") {
			addr = "0x" + addr
		}

		if len(addr) != 42 {
			return ErrAddressInvalid
		}
	}

	_, err := data.GetUserByAddr(addr, targetAddressType)
	if errors.Is(err, data.ErrNotFound) {
		return ErrAddressNotFound
	}

	return err
}

func validateSignature(address string, addressType model.UserSource, signatureData, signature string) error {
	if addressType == model.UserSourceTRON {
		a, err := utils.ExtractTronAddressBySignature(signatureData, signature)
		if err != nil {
			return err
		}
		if address == tron.TronAddressToEtherAddress(a) {
			return nil
		}
		return errors.New("invalid signature")
	}
	if addressType == model.UserSourceBTTC {
		a, err := utils.ExtractBTTCAddressBySignature(signatureData, signature)
		if err != nil {
			return err
		}
		if address == a {
			return nil
		}
		return errors.New("invalid signature")
	}

	return nil
}

func CheckAddress(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.CheckTargetAddressArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = validateAddress(arg.TargetAddress, arg.TargetAddressType)
	if err != nil {
		if errors.Is(err, ErrAddressInvalid) {
			response.BadRequestErr(ctx, response.CodeAddressInvalid, err)
			return
		}
		if errors.Is(err, ErrAddressNotFound) {
			response.BadRequestErr(ctx, response.CodeAddressNotExits, err)
			return
		}
		response.InternalErr(ctx)
		return
	}
	response.OK(ctx)
}
