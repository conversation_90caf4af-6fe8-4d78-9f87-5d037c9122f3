kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: $SERVICE_NAME-ingress
  namespace: $NAMESPACE
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/certificate-arn: $CERT_ARN
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/backend-protocol-version: HTTP1
    alb.ingress.kubernetes.io/healthcheck-path: /ping
    alb.ingress.kubernetes.io/wafv2-acl-arn: "$WAF_ARN"
spec:
  rules:
    - host: $HOSTNAME
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: $SERVICE_NAME-service
                port:
                  name: http
