package s3

import (
	"gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"math/rand"
	"net/url"
	"strings"
)

func getRandomLBHost() string {
	if config.BTFS.VisitType == s3.IP {
		return config.BTFS.S3HostIps[rand.Intn(len(config.BTFS.S3HostIps))]
	}
	if config.BTFS.VisitType == s3.DOMAIN {
		return config.BTFS.S3Host
	}
	panic("visit type doesn't not support please check")
}

var hostIP2S3IP = make(map[string]string)

func init() {
	hostIps := config.BTFS.HostIps
	s3Ips := config.BTFS.S3HostIps
	s3ips := make(map[string]string, len(s3Ips))
	for _, ip := range s3Ips {
		u, err := url.Parse(ip)
		if err != nil {
			continue
		}
		s3ips[u.Hostname()] = ip
	}

	for _, ip := range hostIps {
		u, err := url.Parse(ip)
		if err != nil {
			continue
		}

		hostIP2S3IP[u.Hostname()] = s3ips[u.Hostname()]
	}
}

func getLocationByKeyLocation(location string) string {
	// just for download object
	if !strings.HasPrefix(location, "http://") {
		location = "http://" + strings.Split(location, ":")[0]
	}
	u, _ := url.Parse(location)
	return hostIP2S3IP[u.Hostname()]
}
