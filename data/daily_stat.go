package data

import (
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
	"time"
)

const insertDailyStatRecordSql = `
insert into tb_daily_stat
	(
		stat_date, user_total, site_total,
		site_size_total, site_size_user_total, created_at,
		updated_at
	)
values
	(
		$1, $2, $3,
		$4, $5, $6,
		$7
	)
on conflict 
	(
		stat_date
	)
do update set 
		user_total = $2, 
		site_total = $3,
		site_size_total = $4,
		site_size_user_total = $5,
		updated_at = $7`

func UpsertDailyStatRecord(stat *model.DailyStat) (err error) {
	_, err = postgres.ExecByRW(
		insertDailyStatRecordSql,
		stat.StatDate, stat.UserTotal, stat.SiteTotal,
		stat.SiteSizeTotal, stat.SiteSizeUserTotal, stat.CreatedAt,
		stat.UpdatedAt,
	)
	return
}

const queryDailyStatListSql = `
	select * from tb_daily_stat where 1 = 1%s order by id desc
`

func QueryDailyStatList(from, to *time.Time) (list []*model.DailyStat, err error) {
	var args []interface{}
	var where string
	if from != nil {
		args = append(args, from)
		where += fmt.Sprintf(` and stat_date >= $%d`, len(args))
	}
	if to != nil {
		args = append(args, to)
		where += fmt.Sprintf(` and stat_date <= $%d`, len(args))
	}
	qs := fmt.Sprintf(queryDailyStatListSql, where)
	err = postgres.SelectByRW(qs, &list, args...)
	return
}
