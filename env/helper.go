// Package env help to get system environment variable conveniently
// Supported types: string, int, bool, []string, time.Duration, struct
// Functions with name prefixed "Must" means it will panic on corresponding env not exists
// or cannot be converted to the needed type
// ScanTo use field tag "env" to define corresponding env name and mode announce
// The Mode can be "normal" for default get, "must" for must get, "omitted" for ignore the field
// In normal mode, the field tag "default" used to define default value
// The default value must be converted to the field type, if not, it will panic
// If the "default" tag not exits, the filed type zero value will be used
package env

import (
	"errors"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"
)

const (
	envTag     = "env"
	defaultTag = "default"
)

func convert(from string, to interface{}) (err error) {
	rpv := reflect.ValueOf(to)
	if rpv.IsNil() || rpv.Kind() != reflect.Ptr {
		err = errors.New("scan type must be no-nil pointer")
		return
	}
	rvv := rpv.Elem()
	switch to.(type) {
	case *string:
		rvv.SetString(from)
	case *int, *int8, *int16, *int32, *int64:
		bitSize := rvv.Type().Bits()
		var v int64
		v, err = strconv.ParseInt(from, 10, int(bitSize))
		if err != nil {
			return
		}
		rvv.SetInt(v)
	case *uint, *uint8, *uint16, *uint32, *uint64:
		bitSize := rvv.Type().Bits()
		var v uint64
		v, err = strconv.ParseUint(from, 10, int(bitSize))
		if err != nil {
			return
		}
		rvv.SetUint(v)
	case *float32, *float64:
		bitSize := rvv.Type().Bits()
		var v float64
		v, err = strconv.ParseFloat(from, bitSize)
		rvv.SetFloat(v)
	case *bool:
		var v bool
		v, err = strconv.ParseBool(from)
		if err != nil {
			return
		}
		rvv.SetBool(v)
	case *[]string:
		v := strings.Split(from, ",")
		rvv.Set(reflect.ValueOf(v))
	case *map[string]struct{}:
		v := strings.Split(from, ",")
		m := make(map[string]struct{})
		for _, item := range v {
			m[item] = struct{}{}
		}
		rvv.Set(reflect.ValueOf(m))
	case *map[string]string:
		v := strings.Split(from, ",")
		m := make(map[string]string)
		for _, item := range v {
			pairs := strings.Split(item, ":")
			if len(pairs) < 1 {
				continue
			}
			if len(pairs) == 1 {
				m[pairs[0]] = ""
				continue
			}
			m[pairs[0]] = pairs[1]
		}
		rvv.Set(reflect.ValueOf(m))
	case *time.Duration:
		var v time.Duration
		v, err = time.ParseDuration(from)
		if err != nil {
			return
		}
		rvv.Set(reflect.ValueOf(v))
	case *time.Time:
		var v time.Time
		v, err = time.Parse(time.RFC3339, from)
		if err != nil {
			return
		}
		rvv.Set(reflect.ValueOf(v))
	case *Size:
		var size int64
		size, err = parseHumanSize(from)
		if err != nil {
			return
		}
		rvv.SetInt(size)
	default:
		err = errors.New("unsupported type")
	}
	return
}

func notExistsErr(key string) error {
	return fmt.Errorf("env %s not exists", key)
}

func typeConvertErr(err error) error {
	return fmt.Errorf("convert value: %w", err)
}

func Get(key, defaultStr string) (val string) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		valStr = defaultStr
	}
	if err := convert(valStr, &val); err != nil {
		panic(err)
	}
	return val
}

func MustGet(key string) (val string) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		panic(notExistsErr(key))
	}
	if err := convert(valStr, &val); err != nil {
		panic(err)
	}
	return val
}

func GetInt(key, defaultStr string) (val int) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		valStr = defaultStr
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func MustGetInt(key string) (val int) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		panic(notExistsErr(key))
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func GetBool(key, defaultStr string) (val bool) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		valStr = defaultStr
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func MustGetBool(key string) (val bool) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		panic(notExistsErr(key))
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func GetDuration(key, defaultStr string) (val time.Duration) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		valStr = defaultStr
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func MustGetDuration(key string) (val time.Duration) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		panic(notExistsErr(key))
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func GetArr(key, defaultStr string) (val []string) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		valStr = defaultStr
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func MustGetArr(key string) (val []string) {
	valStr, exist := os.LookupEnv(key)
	if !exist {
		panic(notExistsErr(key))
	}
	if err := convert(valStr, &val); err != nil {
		panic(typeConvertErr(err))
	}
	return val
}

func ScanTo(ptr interface{}) {
	rpv := reflect.ValueOf(ptr)
	if rpv.Kind() != reflect.Ptr || rpv.IsNil() {
		panic("type must be *struct")
	}
	rsv := rpv.Elem()
	rst := rsv.Type()
	for i := 0; i < rsv.NumField(); i++ {
		rfs := rst.Field(i)
		rfv := rsv.Field(i)
		ifp := rfv.Addr().Interface()
		tag := rfs.Tag.Get(envTag)
		var (
			envKey   = strings.ToUpper(rfs.Name)
			announce = "normal"
		)
		if tag != "" {
			parts := strings.Split(tag, ",")
			envKey = parts[0]
			if len(parts) > 1 {
				announce = parts[1]
			}
		}
		switch announce {
		case "omitted":
			continue
		case "must":
			valStr, ok := os.LookupEnv(envKey)
			if !ok {
				panic(fmt.Sprintf("env %s not exists", envKey))
			}
			err := convert(valStr, ifp)
			if err != nil {
				panic(typeConvertErr(err))
			}
		default:
			defaultValPtr := reflect.New(rfs.Type)
			defaultValStr, ok := rfs.Tag.Lookup(defaultTag)
			if ok {
				err := convert(defaultValStr, defaultValPtr.Interface())
				if err != nil {
					panic(fmt.Sprintf("convert default value: %v", err))
				}
			}
			valStr, ok := os.LookupEnv(envKey)
			if !ok {
				rfv.Set(defaultValPtr.Elem())
				continue
			}
			err := convert(valStr, ifp)
			if err != nil {
				panic(typeConvertErr(err))
			}
		}
	}
	return
}
