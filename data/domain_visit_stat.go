package data

import (
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/athena"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
	"time"
)

const getDomainVisitStatFromGatewayLogsSql = `select 
    coalesce(count(tmp.visit), 0) as pv, 
    coalesce(count(distinct tmp.visit), 0) as uv, 
    coalesce(sum(tmp.sent_bytes), 0) as bandwidth 
from (
    select 
        if(coalesce(regexp_extract(request_url, '(\.\w+)($|\?)', 1), '.html')  = '.html', client_ip, null) as visit,
        sent_bytes
    from 
        gateway_logs 
    where 
        regexp_extract(request_url, '^https://(.+?)(:|/)', 1) = '%s'
        and (target_status_code = '200' or target_status_code = '304')
        and day = '%s'
        and time >= '%s'
        and time < '%s'
) as tmp`

func GetDomainVisitStatFromGatewayLogs(domain string, date, from, to time.Time) (stat *model.DomainVisitStat, err error) {
	stat = &model.DomainVisitStat{}
	sqlStr := fmt.Sprintf(
		getDomainVisitStatFromGatewayLogsSql,
		domain,
		date.Format("2006/01/02"),
		from.Format(time.RFC3339Nano),
		to.Format(time.RFC3339Nano),
	)
	err = athena.GetDB().QueryRow(sqlStr).Scan(&stat.PV, &stat.UV, &stat.Bandwidth)
	return
}

const getDomainVisitStatFromGatewayLogsFromPGSql = `select 
    coalesce(count(tmp.visit), 0) as pv, 
    coalesce(count(distinct tmp.visit), 0) as uv, 
    coalesce(sum(tmp.sent_bytes), 0) as bandwidth 
from (
    select 
        CASE WHEN SUBSTRING(request_url FROM '(\.\w+)($|\?)') = '.html' THEN client_ip  ELSE NULL  END AS visit,  
        sent_bytes
    from 
        tb_gateway_logs 
    where 
        substring(request_url from '^https://(.+?)(:|/)') = '%s'
        and (target_status_code = '200' or target_status_code = '304')
        and day = '%s'
        and time >= '%s'
        and time < '%s'
) as tmp`

func GetDomainVisitStatFromPgLog(domain string, date, from, to time.Time) (stat *model.DomainVisitStat, err error) {
	stat = &model.DomainVisitStat{}
	sqlStr := fmt.Sprintf(
		getDomainVisitStatFromGatewayLogsFromPGSql,
		domain,
		date.Format("2006/01/02"),
		from.Format(time.RFC3339Nano),
		to.Format(time.RFC3339Nano),
	)
	err = postgres.SelectFromLog(sqlStr, stat)
	return
}
