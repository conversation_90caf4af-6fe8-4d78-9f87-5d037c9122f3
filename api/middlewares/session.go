package middlewares

import (
	"strings"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var sessStore sessions.Store

const (
	sessionKey    = "xSess"
	sessionSecret = "xSessSecret"
)

func init() {
	// Check if Redis is configured and available
	if config.Redis.Host != "" {
		// Try to use Redis session store
		var client redis.UniversalClient

		if strings.Contains(config.Redis.Host, ",") {
			// Cluster mode
			addrs := strings.Split(config.Redis.Host, ",")
			for i, addr := range addrs {
				addrs[i] = strings.TrimSpace(addr)
			}

			client = redis.NewClusterClient(&redis.ClusterOptions{
				Addrs:          addrs,
				Password:       config.Redis.Password,
				MaxRedirects:   8,
				RouteByLatency: true,
				RouteRandomly:  false,
			})
		} else {
			// Single instance mode
			client = redis.NewClient(&redis.Options{
				Addr:     config.Redis.Host,
				Password: config.Redis.Password,
				DB:       config.Redis.DB,
			})
		}

		// Create Redis session store
		sessStore = NewRedisSessionStore(client, []byte(sessionSecret))
	} else {
		// Fallback to cookie store if Redis is not configured
		sessStore = cookie.NewStore([]byte(sessionSecret))
	}
}

func Session() gin.HandlerFunc {
	return sessions.Sessions(sessionKey, sessStore)
}
