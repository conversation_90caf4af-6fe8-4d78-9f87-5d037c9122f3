package s3

import (
	"bytes"
	"context"
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"time"
)

const (
	IP     = "ip"
	DOMAIN = "domain"
)

type BTFSCmdRequest struct {
	Path   string
	Params url.Values
	Body   []byte
	Extra  map[string]string
}

// BtfsCMDClient call s3 cmd key api
type BtfsCMDClient struct {
	client *http.Client
	Target string
}

func (cbd *BtfsCMDClient) PostProxy(ctx context.Context, req *BTFSCmdRequest) (*http.Response, error) {
	baseURL := cbd.Target + req.Path
	fullURL, err := url.Parse(baseURL)
	if err != nil {
		return nil, err
	}
	fullURL.RawQuery = req.Params.Encode()

	request, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL.String(), bytes.NewBuffer(req.Body))
	if err != nil {
		return nil, err
	}
	return cbd.client.Do(request)
}

func (cbd *BtfsCMDClient) Post(ctx context.Context, req *BTFSCmdRequest) ([]byte, error) {
	baseURL := cbd.Target + req.Path
	fullURL, err := url.Parse(baseURL)
	if err != nil {
		return nil, err
	}
	fullURL.RawQuery = req.Params.Encode()

	request, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL.String(), bytes.NewBuffer(req.Body))
	if err != nil {
		return nil, err
	}
	resp, err := cbd.client.Do(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("the response code from btfs is %d", resp.StatusCode)
	}

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return respBody, nil
}

func GetBTFSCmdClient(kind string) *BtfsCMDClient {
	if kind == IP {
		return &BtfsCMDClient{
			client: btfsClient.client,
			Target: targetLB(config.BTFS.HostIps),
		}
	}

	if kind == DOMAIN {
		return &BtfsCMDClient{
			client: btfsClient.client,
			Target: config.BTFS.Host,
		}
	}

	panic("kind not support, please check")
}

// a random lb
func targetLB(ips []string) string {
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(ips))
	return ips[randomIndex]
}
