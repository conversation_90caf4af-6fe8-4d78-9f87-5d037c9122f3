package handlers

import (
	"encoding/base64"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/github"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
)

var validate = validator.New()

func GithubCallback(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	var arg model.GitHubReq
	err = ctx.ShouldBind(&arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	err = validate.Struct(&arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}
	b, err := base64.URLEncoding.DecodeString(arg.State)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	arg.State = string(b)
	// TODO check site_id
	// if strings.Contains(arg.State, "?") {
	// 	response.ParamErr(ctx, errors.New("state should not contain '?'"))
	// 	return
	// }
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}
	accessToken, err := github.ExchangeAccessToken(arg.Code)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	githubUser, _, err := github.GetGithubUser(accessToken)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	err = data.UpdateUserInfo(user.Address, accessToken, githubUser.GetID(), githubUser.GetLogin())
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	// update user info in redis session
	u, err := data.GetUserByUserId(user.UserId)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	err = cctx.SetLoginUser(ctx, u)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	ctx.Redirect(http.StatusMovedPermanently, arg.State)
}

func DisconnectGithub(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	err = data.UpdateUserInfo(user.Address, "", 0, "")
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.OK(ctx)
}
