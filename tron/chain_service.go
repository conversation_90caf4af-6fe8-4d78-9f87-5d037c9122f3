package tron

import (
	"context"
)

type ChainService interface {
	GetEventByContract(ctx context.Context, arg *GetEventByContractArg) (rsp *GetEventByContractResponseBody, err error)
	TriggerConstantContract(ctx context.Context, arg *TriggerConstantContractArg) (rsp *TriggerConstantContractResponseBody, err error)
}

type GetEventByContractArg struct {
	Address           string `json:"address"`
	EventName         string `json:"event_name"`
	BlockNumber       int64  `json:"block_number"`
	OnlyUnConfirmed   bool   `json:"only_un_confirmed"`
	OnlyConfirmed     bool   `json:"only_confirmed"`
	MinBlockTimestamp int64  `json:"min_block_timestamp"`
	MaxBlockTimestamp int64  `json:"max_block_timestamp"`
	OrderBy           string `json:"order_by"` // block_timestamp,desc | block_timestamp,asc
	Fingerprint       string `json:"fingerprint"`
	Limit             int32  `json:"limit"` // Number of transactions per page, default 20, max 200
}

type GetEventByContractResponseDataItem struct {
	BlockNumber           int64             `json:"block_number"`
	BlockTimestamp        int64             `json:"block_timestamp"`
	CallerContractAddress string            `json:"caller_contract_address"`
	ContractAddress       string            `json:"contract_address"`
	EventIndex            int64             `json:"event_index"`
	EventName             string            `json:"event_name"`
	Result                map[string]string `json:"result"`
	ResultType            map[string]string `json:"result_type"`
	Event                 string            `json:"event"`
	TransactionId         string            `json:"transaction_id"`
}

type GetEventByContractResponseMeta struct {
	At          int64  `json:"at"`
	Fingerprint string `json:"fingerprint"`
	Links       struct {
		Next string `json:"next"`
	} `json:"links"`
	PageSize int32 `json:"page_size"`
}

type GetEventByContractResponseBody struct {
	Data    []*GetEventByContractResponseDataItem `json:"data"`
	Success bool                                  `json:"success"`
	Meta    GetEventByContractResponseMeta        `json:"meta"`
}

type TriggerConstantContractArg struct {
	OwnerAddress     string `json:"owner_address"`
	ContractAddress  string `json:"contract_address"`
	FunctionSelector string `json:"function_selector"`
	Parameter        string `json:"parameter"`
}

type TriggerConstantContractResponseBody struct {
	ConstantResult []string `json:"constant_result"`
}
