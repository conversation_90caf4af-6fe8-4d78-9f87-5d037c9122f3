package s3

import (
	"context"
	"encoding/json"
	"errors"
	"gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"net/url"
)

type KeyAction string

const (
	Disable KeyAction = "disable"
	Enable  KeyAction = "enable"
	Delete  KeyAction = "delete"
	Reset   KeyAction = "reset"
)

var (
	ErrorKeyNotFound       = errors.New("key not found")
	ErrorKeyNotMatchUserId = errors.New("userId not match")
)

func GenerateKey(ctx context.Context, userId string) ([]byte, error) {

	req := &s3.BTFSCmdRequest{Path: config.BTFS.GenerateKey}

	client := s3.GetBTFSCmdClient(config.BTFS.VisitType)
	resp, err := client.Post(ctx, req)

	if err != nil {
		return nil, err
	}

	btfsResp := &model.GenerateKeyResp{}
	err = json.Unmarshal(resp, btfsResp)
	if err != nil {
		return nil, err
	}

	key := &model.Key{
		UserId:    userId,
		AccessKey: btfsResp.Key,
		Secret:    btfsResp.Secret,
		IsEnabled: btfsResp.Enable,
		IsDeleted: btfsResp.IsDelete,
		Location:  client.Target,
	}

	err = data.CreateKey(ctx, key)
	if err != nil {
		return nil, err
	}

	return resp, err
}

func ManageKey(ctx context.Context, req *model.ActiveKeyReq, userId string, action KeyAction) ([]byte, error) {
	// check if the key belongs the user
	key, err := data.DetailKey(ctx, req.Key)
	if err != nil {
		return nil, err
	}
	if key == nil {
		return nil, ErrorKeyNotFound
	}
	if key.UserId != userId {
		return nil, ErrorKeyNotMatchUserId
	}

	args := url.Values{}
	args.Add("arg", req.Key)

	path := ""
	switch action {
	case Disable:
		path = config.BTFS.DisableKey
	case Enable:
		path = config.BTFS.EnableKey
	case Delete:
		path = config.BTFS.DeleteKey
	case Reset:
		path = config.BTFS.ResetKey
	default:
		panic("action does not support")
	}

	request := &s3.BTFSCmdRequest{
		Path:   path,
		Params: args,
	}

	client := s3.GetBTFSCmdClient(config.BTFS.VisitType)
	client.Target = key.Location

	resp, err := client.Post(ctx, request)
	if err != nil {
		return nil, err
	}

	if action == Delete {
		err = data.DeleteKey(req.Key)
	}

	if action == Disable {
		err = data.UpdateActiveState(ctx, req.Key, false)
	}

	if action == Enable {
		err = data.UpdateActiveState(ctx, req.Key, true)
	}

	if action == Reset {
		request.Path = config.BTFS.GetKey
		type KeyResp struct {
			Key    string `json:"key"`
			Secret string `json:"secret"`
		}
		res := &KeyResp{}
		resp, err = client.Post(ctx, request)
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(resp, res)
		if err != nil {
			return nil, err
		}

		err = data.ResetKey(req.Key, res.Secret)
		return resp, err
	}

	return resp, err
}

func ListKey(ctx context.Context, userId string) ([]*model.KeyResponse, error) {
	models, err := data.ListKey(ctx, userId)
	if err != nil {
		return nil, err
	}
	resp := make([]*model.KeyResponse, 0)
	for _, m := range models {
		resp = append(resp, &model.KeyResponse{
			Id:        m.Id,
			UserId:    m.UserId,
			AccessKey: m.AccessKey,
			Secret:    m.Secret,
			IsEnabled: m.IsEnabled,
			IsDeleted: m.IsDeleted,
			CreatedAt: m.CreatedAt,
			UpdatedAt: m.UpdatedAt,
		})
	}
	return resp, nil
}

func DetailKey(ctx context.Context, key, userId string) (*model.KeyResponse, error) {
	k, err := data.DetailKey(ctx, key)
	if err != nil {
		return nil, err
	}

	if k == nil {
		return nil, ErrorKeyNotFound
	}

	if k.UserId != userId {
		return nil, ErrorKeyNotMatchUserId
	}

	resp := &model.KeyResponse{
		Id:        k.Id,
		UserId:    k.UserId,
		AccessKey: k.AccessKey,
		Secret:    k.Secret,
		IsEnabled: k.IsEnabled,
		IsDeleted: k.IsDeleted,
		CreatedAt: k.CreatedAt,
		UpdatedAt: k.UpdatedAt,
	}
	return resp, nil
}
