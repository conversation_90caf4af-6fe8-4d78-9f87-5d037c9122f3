package utils

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"strings"

	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/crypto/secp256k1"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
)

const TRON_HEADER = "\x19TRON Signed Message:\n32"

type Address [21]byte

func ExtractTronAddressBySignature(signatureData string, signature string) (string, error) {
	signature = strings.TrimPrefix(signature, "0x")
	signatureByte, err := hex.DecodeString(signature)
	if err != nil {
		return "", err
	}
	if len(signatureByte) != 65 {
		return "", errors.New("incorrect length of signature ")
	}
	if signatureByte[64] >= 27 { // 最后一字节特殊算法逻辑
		signatureByte[64] -= 27
	}

	// digest
	toDigest := TRON_HEADER + string(signatureData[:])
	digest := crypto.Keccak256Hash([]byte(toDigest))

	// 还原公钥
	publicKeyData, err := secp256k1.RecoverPubkey(digest[:], signatureByte)
	if err != nil {
		return "", err
	}
	publicKey := hex.EncodeToString(publicKeyData)

	// 生成address
	keyData, err := hex.DecodeString(publicKey)
	if err != nil {
		return "", err
	}

	ecdsaPubKey, err := crypto.UnmarshalPubkey(keyData)
	if err != nil {
		return "", err
	}

	addr := crypto.PubkeyToAddress(*ecdsaPubKey)

	addressTron := make([]byte, 21)

	addressPrefix, err := hex.DecodeString("41"[:])
	if err != nil {
		return "", err
	}

	addressTron = append(addressTron, addressPrefix...)
	addressTron = append(addressTron, addr.Bytes()...)

	var a Address
	if len(addressTron) > len(a) {
		addressTron = addressTron[len(addressTron)-21:]
	}
	copy(a[21-len(addressTron):], addressTron)

	h0, err := Hash(a[:])
	if err != nil {
		return "", err
	}
	h1, err := Hash(h0)
	if err != nil {
		return "", err
	}
	if len(h1) < 4 {
		return "", errors.New("base58 encode length error")
	}
	inputCheck := append(addressTron, h1[:4]...)

	return base58.Encode(inputCheck), nil
}

func Hash(s []byte) ([]byte, error) {
	h := sha256.New()
	_, err := h.Write(s)
	if err != nil {
		return nil, err
	}
	bs := h.Sum(nil)
	return bs, nil
}

func ExtractBTTCAddressBySignature(signatureData string, signature string) (string, error) {
	odata := []byte("\x19Ethereum Signed Message:\n" + fmt.Sprint(len(signatureData)) + signatureData)
	hash := crypto.Keccak256Hash(odata)
	b, err := hexutil.Decode(signature)
	if err != nil {
		return "", err
	}
	// https://github.com/ethereum/go-ethereum/issues/19751
	if len(b) != 65 {
		return "", errors.New("the length of signature is incorrect")
	}
	b[64] = b[64] - 27
	sigPublicKey, err := crypto.Ecrecover(hash.Bytes(), b)
	if err != nil {
		return "", err
	}
	pk, err := crypto.UnmarshalPubkey(sigPublicKey)
	if err != nil {
		return "", err
	}
	signAddress := crypto.PubkeyToAddress(*pk)
	return strings.ToLower(signAddress.Hex()), nil
}

func help(address, msg, sig string) {
	msgHash := crypto.Keccak256Hash([]byte(msg))
	bytes1 := msgHash.Bytes()
	bytes2 := []byte("\x19TRON Signed Message:\n32")
	toDigest := bytes.Join([][]byte{bytes2, bytes1}, []byte(""))
	digest := crypto.Keccak256Hash(toDigest)
	digestByte := digest.Bytes()

	sigBytes, err := hexutil.Decode(sig)
	if err != nil {
		return
	}
	// https://github.com/ethereum/go-ethereum/issues/19751
	if len(sigBytes) != 65 {
		return
	}
	sigBytes[64] -= 27

	pubKeyRaw, err := crypto.Ecrecover(digestByte, sigBytes)
	if err != nil {
		return
	}
	pubKey, err := crypto.UnmarshalPubkey(pubKeyRaw)
	if err != nil {
		return
	}
	signerAddr := crypto.PubkeyToAddress(*pubKey)
	fmt.Println(signerAddr.String())
	fmt.Println(tron.HexAddressToBase58Address(signerAddr.Hex()))
	fmt.Println(tron.TronAddressToEtherAddress(tron.HexAddressToBase58Address(signerAddr.Hex())))
	fmt.Println(signerAddr.Hex())
	if !strings.EqualFold(signerAddr.Hex(), address) {
		return
	}
}

func world(message, signatureHex string) {
	// message := "Your message here"

	// 对消息进行哈希处理
	hash := crypto.Keccak256Hash([]byte(message))

	// 假设有一个签名（使用私钥对哈希进行签名生成的签名）
	// 这里使用一个已存在的签名进行示例（十六进制编码）
	// signatureHex := "your_signature_here_in_hex"

	// 转换签名为字节
	signatureBytes, err := hex.DecodeString(signatureHex)
	if err != nil {
		log.Fatalf("无法解码签名: %v", err)
	}

	// 解析出公钥
	publicKey, err := crypto.SigToPub(hash.Bytes(), signatureBytes)
	if err != nil {
		log.Fatalf("无法从签名中提取公钥: %v", err)
	}

	// 使用公钥生成地址
	addressBytes := crypto.PubkeyToAddress(*publicKey).Bytes()

	// 将地址转换为 Tron 格式
	tronAddress := base58.CheckEncode(addressBytes, 0x41) // Tron 地址前缀 0x41

	fmt.Println("提取的地址:", tronAddress)
}
