package main

import (
	"github.com/urfave/cli/v2"
	"gitlab.insidebt.net/btfs/storage3-backend/api"
	"gitlab.insidebt.net/btfs/storage3-backend/jobs"
)

var cmds = &cli.App{
	Name:    name,
	Version: version,
	Usage:   "",
	Commands: []*cli.Command{
		{
			Name: "run_api",
			Action: func(ctx *cli.Context) error {
				return api.RunServer(ctx.Context)
			},
		},
		{
			Name: "run_jobs",
			Action: func(ctx *cli.Context) (err error) {
				return jobs.Run(ctx.Context)
			},
		},
	},
}
