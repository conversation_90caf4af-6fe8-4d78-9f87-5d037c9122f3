package postgres

import (
	"context"
	"github.com/jackc/pgx/v4"
)

func ExecByRW(sql string, args ...interface{}) (CmdTag, error) {
	otag, err := rwPool.Exec(context.Background(), sql, args...)
	return CmdTag{otag: otag}, err
}

func ExecByRO(sql string, args ...interface{}) (CmdTag, error) {
	otag, err := roPool.Exec(context.Background(), sql, args...)
	return CmdTag{otag: otag}, err
}

func GetByRW(query string, dst interface{}, args ...interface{}) error {
	return scan.Get(context.Background(), rwPool, dst, query, args...)
}

func GetByRO(query string, dst interface{}, args ...interface{}) error {
	return scan.Get(context.Background(), roPool, dst, query, args...)
}

func SelectByRW(query string, dst interface{}, args ...interface{}) error {
	return scan.Select(context.Background(), rwPool, dst, query, args...)
}

func SelectByRO(query string, dst interface{}, args ...interface{}) error {
	return scan.Select(context.Background(), roPool, dst, query, args...)
}

func BeginFuncByRW(f func(tx Tx) error) error {
	return rwPool.BeginFunc(
		context.Background(), func(otx pgx.Tx) error {
			return f(Tx{otx: otx})
		},
	)
}

func BeginFuncByRO(f func(tx Tx) error) error {
	return roPool.BeginFunc(
		context.Background(), func(otx pgx.Tx) error {
			return f(Tx{otx: otx})
		},
	)
}

func SelectFromLog(query string, dst interface{}, args ...interface{}) error {
	return scan.Get(context.Background(), logPool, dst, query, args...)
}
