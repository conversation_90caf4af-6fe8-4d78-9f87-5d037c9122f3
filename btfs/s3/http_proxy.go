package s3

import (
	"net/http"
	"net/url"
)

// btfsS3ApiClient call just for s3 api

type BizInfo struct {
	AccessKey  string
	BucketName string
	ObjectName string
	Location   string
}

type BeforeInterceptor func(bizInfo *BizInfo, request *http.Request) (*http.Request, string, error)

type AfterInterceptor func(bizInfo *BizInfo, response *http.Response) (*http.Response, error)

type HttpProxy interface {
	DoProxy(request *http.Request) (*http.Response, error)
}

type BtfsProxy struct {
	Before BeforeInterceptor
	After  AfterInterceptor
}

func (proxy *BtfsProxy) DoProxy(bizInfo *BizInfo, request *http.Request) (*http.Response, error) {
	request, location, err := proxy.Before(bizInfo, request)
	if err != nil {
		return nil, err
	}

	base := request.Host + request.URL.Path
	if location != "" {
		base = location + request.URL.Path
	}
	fullURL, err := url.Parse(base)
	if err != nil {
		return nil, err
	}
	fullURL.RawQuery = request.URL.RawQuery

	req, err := http.NewRequest(request.Method, fullURL.String(), request.Body)
	req.Header = request.Header
	req.ContentLength = request.ContentLength
	resp, err := btfsClient.client.Do(req)
	if err != nil {
		return resp, err
	}
	resp.Request = request
	bizInfo.Location = request.Host

	if proxy.After != nil {
		resp, err = proxy.After(bizInfo, resp)
	}

	return resp, err

}
