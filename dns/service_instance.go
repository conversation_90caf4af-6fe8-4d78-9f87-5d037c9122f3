package dns

import (
	"errors"
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/route53"
)

const (
	upsertAction = "UPSERT"
	deleteAction = "DELETE"
)

type service struct {
	client     *route53.Route53
	rootDomain string
	zoneId     string
}

func newService(rootDomain, zoneId string) (svc *service, err error) {
	sess, err := session.NewSession()
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}
	cli := route53.New(sess)
	svc = &service{
		client:     cli,
		rootDomain: rootDomain,
		zoneId:     zoneId,
	}
	return
}

func (s *service) GetRootDomain() string {
	return s.rootDomain
}

func (s *service) UpsertDNSLinkRecord(subdomain, dnslink string) (ChangeInfo ChangeInfo, err error) {
	return s.changeDNSLinkRecord(subdomain, dnslink, upsertAction)
}

func (s *service) DeleteDNSLinkRecord(subdomain, dnslink string) (ChangeInfo ChangeInfo, err error) {
	return s.changeDNSLinkRecord(subdomain, dnslink, deleteAction)
}

func (s *service) changeDNSLinkRecord(subdomain, dnslink string, action string) (changeInfo ChangeInfo, err error) {
	name := fmt.Sprintf("_dnslink.%s.%s", subdomain, s.rootDomain)
	target := fmt.Sprintf(`"dnslink=%s"`, dnslink)

	change := &route53.Change{
		Action: aws.String(action),
		ResourceRecordSet: &route53.ResourceRecordSet{
			Name: aws.String(name),
			Type: aws.String("TXT"),
			ResourceRecords: []*route53.ResourceRecord{
				{
					Value: aws.String(target),
				},
			},
			TTL: aws.Int64(60),
		},
	}

	params := &route53.ChangeResourceRecordSetsInput{
		ChangeBatch: &route53.ChangeBatch{
			Changes: []*route53.Change{change},
		},
		HostedZoneId: aws.String(s.zoneId),
	}

	output, err := s.client.ChangeResourceRecordSets(params)
	if err != nil {
		return
	}
	if output == nil || output.ChangeInfo == nil {
		err = errors.New("no result")
		return
	}
	changeInfo = output.ChangeInfo

	return
}
