package handlers

import (
	"errors"
	"fmt"

	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/github"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/services/hosting"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
)

// signatureHeader is the GitHub header key used to pass the HMAC hexdigest.
var signatureHeader = "X-Hub-Signature"

func GetUserSiteList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.QuerySiteListArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	// 查询转移中的站点：
	// 1. 转移出去的对方还没有接收
	// 2. 别人转给自己，自己还没有接收
	pendingSites := make([]*model.SiteTransferRecord, 0)
	pendingSites, err = hosting.GetTransferPendingSite(user)
	if err != nil && !errors.Is(err, data.ErrNotFound) {
		response.InternalErr(ctx)
		return
	}

	pendingSiteIds := make([]string, 0)
	for _, v := range pendingSites {
		// 别人转给自己，自己还没有接收的站点，需要根据sit_id查询
		if v.ToAddress == user.Address {
			pendingSiteIds = append(pendingSiteIds, fmt.Sprintf(`'%s'`, v.SiteId))
		}
	}

	// 1. 查询自己的 user
	// 2. 别人转给自己但是自己没有接收的 pendingSiteIds
	list, total, err := hosting.QueryUserSiteList(user, pendingSiteIds, arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	siteIds := make([]string, 0)
	for _, v := range list {
		siteIds = append(siteIds, fmt.Sprintf(`'%s'`, v.SiteId))
	}

	// 查询这些站点中 哪些是新接收的 用来设置每个站点的transfer_status
	// 存在一种情况，接收了transfer_status = 2，但是没有进行部署，而是直接转给别人了
	// 此时 tb_site_transfer_record表中有两个记录, 但是此时transfer_status=2的记录应该是无效的了
	acceptedSites := make([]*model.SiteTransferRecord, 0)
	if len(siteIds) > 0 {
		acceptedSites, err = hosting.GetTransferAcceptedSite(siteIds, user.Address)
		if err != nil && !errors.Is(err, data.ErrNotFound) {
			response.InternalErr(ctx)
			return
		}
	}

	// 如果在pending里并且是自己转移出去的就从accept里删除
	filterAcceptedSites := make([]*model.SiteTransferRecord, 0)
	pending := make(map[string]bool)
	for _, p := range pendingSites {
		if p.FromAddress == user.Address || p.ToAddress == user.Address {
			pending[p.SiteId] = true
		}
	}

	for i, a := range acceptedSites {
		if pending[a.SiteId] {
			continue
		}
		filterAcceptedSites = append(filterAcceptedSites, acceptedSites[i])
	}

	pendingSites = append(pendingSites, filterAcceptedSites...)

	moreList, err := packGetSiteVisitStatList(user.UserId, user.Address, list, pendingSites)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"total": total,
		"list":  moreList,
	})
}

func packGetSiteVisitStatList(userId string, address string, siteList []*model.Site, transferringSite []*model.SiteTransferRecord) (s []*model.SiteStatMore, err error) {
	if len(siteList) <= 0 {
		return s, nil
	}

	siteStatus := make(map[string]*model.SiteTransferRecord, len(transferringSite))

	for _, v := range transferringSite {
		siteStatus[v.SiteId] = v
	}

	// 组siteIdList
	siteIdList := make([]string, 0)
	for _, v := range siteList {
		siteIdList = append(siteIdList, v.SiteId)
	}

	// 查询昨天统计数据，site_id顺序完全一样
	statList, err := hosting.GetSiteLastVisitStatList(siteIdList)
	if err != nil {
		return nil, err
	}

	// 组包含昨天统计的数据
	s = make([]*model.SiteStatMore, 0)
	for i, v := range siteList {
		one := &model.SiteStatMore{}
		one.SiteId = v.SiteId
		one.UserId = v.UserId
		one.SiteName = v.SiteName
		one.Domain = v.Domain
		one.RepoOwner = v.RepoOwner
		one.Repo = v.Repo
		one.Branch = v.Branch
		one.BuildImage = v.BuildImage
		one.BuildCommand = v.BuildCommand
		one.PublishDirectory = v.PublishDirectory
		one.BaseDirectory = v.BaseDirectory
		one.LatestPublishedDeployId = v.LatestPublishedDeployId
		one.CurrentFileHash = v.CurrentFileHash
		one.CurrentFileSize = v.CurrentFileSize
		one.LatestPublishedAt = v.LatestPublishedAt
		one.IsDeleted = v.IsDeleted
		one.CreatedAt = v.CreatedAt
		one.UpdatedAt = v.UpdatedAt
		one.CurrentSignature = v.CurrentSignature
		one.CurrentSignatureMessage = v.CurrentSignatureMessage
		one.OnceSigned = v.OnceSigned
		one.DeploymentDuration = v.DeploymentDuration

		if statList[i] != nil {
			one.LastDailyStat.Uv = statList[i].Uv
			one.LastDailyStat.Pv = statList[i].Pv
			one.LastDailyStat.Bandwidth = statList[i].Bandwidth
		}

		if siteStatus[v.SiteId] != nil {
			if siteStatus[v.SiteId].TransferStatus == model.TransferStatusPending && siteStatus[v.SiteId].FromUserId == userId {
				one.TransferStatus = model.TransferStatusInitiatorPending
			} else if siteStatus[v.SiteId].TransferStatus == model.TransferStatusPending && siteStatus[v.SiteId].ToAddress == address {
				one.TransferStatus = model.TransferStatuReceivePending
			} else if siteStatus[v.SiteId].TransferStatus == model.TransferStatusAccepted && siteStatus[v.SiteId].ToAddress == address {
				one.TransferStatus = model.TransferStatusReceived
			}
			one.TransferCreatedAt = siteStatus[v.SiteId].CreatedAt
			one.TransferFrom = siteStatus[v.SiteId].FromUserId
			if siteStatus[v.SiteId].FromAddressType == model.UserSourceTRON {
				one.TransferFromAddress = tron.HexAddressToBase58Address(siteStatus[v.SiteId].FromAddress)
			} else {
				one.TransferFromAddress = siteStatus[v.SiteId].FromAddress
			}

		}

		s = append(s, one)
	}

	return s, nil
}

func GetUserSiteDetail(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.QuerySiteDetailArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	detail, err := hosting.QueryUserSiteDetail(user, arg)
	if err != nil && !errors.Is(err, data.ErrNotFound) {
		response.InternalErr(ctx)
		return
	}
	var s *model.SiteTransferRecord
	// 查询别人转给自己，或者自己转移给别人的站点
	s, err = hosting.GetTransferringSiteBySiteId(arg.SiteId, user, true)
	if err != nil && !errors.Is(err, data.ErrNotFound) {
		response.InternalErr(ctx)
		return
	}

	// 如果是别人转移给自己的, 需要用转移给自己的那个人的信息去查这个站点
	if detail.SiteId == "" && s.SiteId != "" {
		user1 := &model.User{UserId: s.FromUserId}
		detail, err = hosting.QueryUserSiteDetail(user1, arg)
		if err != nil && !errors.Is(err, data.ErrNotFound) {
			response.InternalErr(ctx)
			return
		}
	}

	if detail == nil || detail.SiteId == "" {
		response.NotFound(ctx)
		return
	}

	if s != nil {
		// 自己转移给别人的
		if s.TransferStatus == model.TransferStatusPending && s.FromUserId == user.UserId {
			detail.TransferStatus = model.TransferStatusInitiatorPending
			// 别人转移给自己的， 还没有接受
		} else if s.TransferStatus == model.TransferStatusPending && s.ToAddress == user.Address {
			detail.TransferStatus = model.TransferStatuReceivePending
			// 别人转移给自己的，并且已经接受
		} else if s.TransferStatus == model.TransferStatusAccepted && s.ToAddress == user.Address {
			detail.TransferStatus = model.TransferStatusReceived
			// 自己转移给别人，别人拒绝了自己的转移, 需要查询上一条记录的状态来确定 是不是首次部署
		} else if s.TransferStatus == model.TransferStatusDeclined && s.FromUserId == user.UserId {
			var lastRecord *model.SiteTransferRecord
			lastRecord, err = hosting.GetTransferringSiteBySiteId(arg.SiteId, user, false)
			if err != nil && !errors.Is(err, data.ErrNotFound) {
				response.InternalErr(ctx)
				return
			}
			if lastRecord != nil && lastRecord.TransferStatus == model.TransferStatusAccepted {
				detail.TransferStatus = model.TransferStatusReceived
			}
		}

		if s.FromAddressType == model.UserSourceTRON {
			detail.TransferFromAddress = tron.HexAddressToBase58Address(s.FromAddress)
		} else {
			detail.TransferFromAddress = s.FromAddress
		}
	}

	response.DataOK(ctx, response.Data{
		"detail": detail,
	})
}

func UpdateUserSiteName(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.UpdateSiteNameArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.UpdateUserSiteName(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func UpdateSiteAutoDeploy(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.UpdateSiteAutoDeployArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.UpdateSiteAutoDeploy(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func RegenerateSiteAutoDeploySecret(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.RegenerateSiteAutoDeploySecretArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.RegenerateSiteAutoDeploySecret(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func UpdateUserDeploySignature(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.UpdateDeploySignatureArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = validateSignature(user.Address, user.Source, arg.SignatureMessage, arg.Signature)
	if err != nil {
		response.BadRequestErr(ctx, response.CodeInvalidSignature, err)
		return
	}

	err = hosting.UpdateUserDeploySignature(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func UpdateUserSiteSetting(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.UpdateSiteSettingArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.UpdateUserSiteSetting(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func DeleteUserSite(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.DeleteSiteArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.DeleteUserSite(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func GetUserDeployList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.QueryDeployListArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	list, total, err := hosting.QueryUserDeployList(user, arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"total": total,
		"list":  list,
	})
}

func GetSignedDeployList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.QueryDeployListArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	list, total, err := hosting.GetSignedDeployList(arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	packList, err := packGetSignedDeployList(list)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"total": total,
		"list":  packList,
	})
}

func packGetSignedDeployList(list []*model.DeployListItemNonLogin) (s []*model.RespGetSignedDeployList, err error) {
	if len(list) <= 0 {
		return s, nil
	}

	// get user
	user, err := data.GetUserByUserId(list[0].UserId)
	if err != nil {
		return s, err
	}

	// set one
	s = make([]*model.RespGetSignedDeployList, 0)
	for _, deploy := range list {
		one := &model.RespGetSignedDeployList{
			DeployListItemNonLogin: *deploy,
		}

		one.Address = user.Address
		one.Source = user.Source
		one.GithubRepoPath = fmt.Sprintf("https://github.com/%s/%s", deploy.RepoOwner, deploy.Repo)

		s = append(s, one)
	}

	return s, nil
}

func GetUserDeployDetail(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.QueryDeployDetailArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	detail, err := hosting.QueryUserDeployDetail(user, arg)
	if err != nil {
		if err == hosting.ErrNotFound {
			response.NotFound(ctx)
		} else {
			response.InternalErr(ctx)
		}
		return
	}

	response.DataOK(ctx, response.Data{
		"detail": detail,
	})
}

func CreateUserSiteAndTriggerUserDeploy(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.CreateSiteArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.IsBuildImageAndCommandAllowed(arg.BuildImage, arg.BuildCommand)
	if err != nil {
		response.BadRequestErr(ctx, response.CodeNotAllowedToDeploy, err)
		return
	}

	siteId, deployId, err := hosting.CreateUserSiteAndTriggerUserDeploy(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.DataOK(ctx, response.Data{
		"site_id":   siteId,
		"deploy_id": deployId,
	})
}

func TriggerUserDeploy(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.TriggerDeployArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	siteId, deployId, err := hosting.TriggerUserDeploy(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.DataOK(ctx, response.Data{
		"site_id":   siteId,
		"deploy_id": deployId,
	})
}

func TriggerDeployByGithub(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	body, pushEvent, err := github.ParseWebHook(ctx.Request)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}
	var repoArg = &model.QuerySiteDetailByRepoArg{
		RepoOwner: *pushEvent.Repo.Owner.Name,
		Repo:      *pushEvent.Repo.Name,
	}
	details, err := hosting.QuerySiteDetailByRepo(repoArg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}
	sig := ctx.Request.Header.Get(signatureHeader)
	for _, site := range details {
		err := github.ValidateSignature(sig, body, []byte(site.AutoDeploySecret))
		if err != nil {
			continue
		}
		u, err := data.GetUserByUserId(site.UserId)
		if err != nil {
			response.HostingOperationErr(ctx, err)
			return
		}
		var arg = &model.TriggerDeployArg{
			SiteId: site.SiteId,
		}

		siteId, deployId, err := hosting.TriggerUserDeploy(u, arg)
		if err != nil {
			response.HostingOperationErr(ctx, err)
			return
		}
		response.DataOK(ctx, response.Data{
			"site_id":   siteId,
			"deploy_id": deployId,
		})
		return
	}
}

func CancelUserDeploy(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	arg := &model.CancelDeployArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = hosting.CancelUserDeploy(user, arg)
	if err != nil {
		response.HostingOperationErr(ctx, err)
		return
	}

	response.OK(ctx)
}

func GetPresetBuildSettingList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	list, total, err := hosting.QueryPresetBuildSettingList()
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"total": total,
		"list":  list,
	})
}

func BatchGetSiteInfo(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.BatchGetSiteInfoArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	sites, err := hosting.QuerySiteDetailsBySiteIds(arg.SiteIdList)
	if err != nil {
		if err == hosting.ErrNotFound || len(sites) == 0 {
			response.NotFound(ctx)
		} else {
			response.InternalErr(ctx)
		}
		return
	}

	list, err := packBatchGetSiteInfo(arg.SiteIdList, sites)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"list": list,
	})
}

func GetSiteDailyVisitStatList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.GetSiteDailyVisitStatListArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	statList, err := hosting.GetSiteLastNVisitDaysStatList(arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	// 反转一下list，使时间顺序处理
	length := len(statList)
	list := make([]*model.RespSiteDailyVisitStatList, 0)
	for i := 0; i < length; i++ {
		list = append(list, statList[length-i-1])
	}

	response.DataOK(ctx, response.Data{
		"list": list,
	})
}

const (
	pvFactor = 321
	uvFactor = 654
)

func packBatchGetSiteInfo(siteIdList []string, sites []*model.Site) (s []*model.GetSiteSignatureStatListRetOne, err error) {
	statList, err := hosting.GetSite7daysVisitStatList(siteIdList)
	if err != nil {
		return nil, err
	}

	mp := make(map[string]*model.Site)
	for _, v := range sites {
		mp[v.SiteId] = v
	}

	s = make([]*model.GetSiteSignatureStatListRetOne, 0)
	for i, siteId := range siteIdList {
		one := &model.GetSiteSignatureStatListRetOne{
			SiteId: siteId,
		}

		v, bl := mp[siteId]
		if bl {
			one.SiteName = v.SiteName
			one.Domain = v.Domain
			one.OnceSigned = v.OnceSigned
			one.CurrentSignature = v.CurrentSignature
			one.CurrentSignatureMessage = v.CurrentSignatureMessage
			one.LastDeploymentTime = v.LatestPublishedAt

			if statList[i] != nil {
				one.PvSortWeight = statList[i].Pv * pvFactor
				one.UvSortWeight = statList[i].Uv * uvFactor
			}
		}

		s = append(s, one)
	}

	return s, nil
}

func CheckSiteName(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.QuerySiteDetailBySiteNameArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	detail, err := hosting.QuerySiteDetailBySiteName(arg)
	if err != nil {
		if err == hosting.ErrNotFound {
			response.NotFound(ctx)
		} else {
			response.InternalErr(ctx)
		}
		return
	}

	var commitId string
	if len(detail.LatestPublishedDeployId) > 0 {
		deploy, err := hosting.QueryUserDeployDetailByDeployId(detail.LatestPublishedDeployId)
		if err != nil {
			if err == hosting.ErrNotFound {
				response.NotFound(ctx)
			} else {
				response.InternalErr(ctx)
			}
			return
		}

		commitId = deploy.CommitId
	}

	user, err := data.GetUserByUserId(detail.UserId)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	siteMore := &model.SiteMore{}
	siteMore.SiteId = detail.SiteId
	siteMore.UserId = detail.UserId
	siteMore.SiteName = detail.SiteName
	siteMore.Domain = detail.Domain
	siteMore.Repo = detail.Repo
	siteMore.RepoOwner = detail.RepoOwner
	if siteMore.RepoOwner == "" {
		siteMore.RepoOwner = user.GithubLogin
	}
	siteMore.Branch = detail.Branch
	siteMore.BuildImage = detail.BuildImage
	siteMore.BuildCommand = detail.BuildCommand
	siteMore.PublishDirectory = detail.PublishDirectory
	siteMore.BaseDirectory = detail.BaseDirectory
	siteMore.LatestPublishedDeployId = detail.LatestPublishedDeployId
	siteMore.CurrentFileHash = detail.CurrentFileHash
	siteMore.CurrentFileSize = detail.CurrentFileSize
	siteMore.LatestPublishedAt = detail.LatestPublishedAt
	siteMore.IsDeleted = detail.IsDeleted
	siteMore.CreatedAt = detail.CreatedAt
	siteMore.UpdatedAt = detail.UpdatedAt
	siteMore.CurrentSignature = detail.CurrentSignature
	siteMore.CurrentSignatureMessage = detail.CurrentSignatureMessage
	siteMore.OnceSigned = detail.OnceSigned

	siteMore.Address = user.Address
	siteMore.Source = user.Source
	siteMore.GithubLogin = user.GithubLogin

	siteMore.GithubRepoPath = fmt.Sprintf("https://github.com/%s/%s", detail.RepoOwner, detail.Repo)

	siteMore.CommitId = commitId

	response.DataOK(ctx, response.Data{
		"site_more_info": siteMore,
	})
}

func CheckWhiteList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	err = hosting.IsUserInWhitelist(user)

	if err != nil {
		response.BadRequestErr(ctx, response.CodeNotAllowedToDeploy, err)
		return
	}

	response.OK(ctx)
}
