package athena

import (
	"database/sql"
	drv "github.com/uber/athenadriver/go"
	"gitlab.insidebt.net/btfs/storage3-backend/clean"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var db *sql.DB

func init() {
	var err error
	defer func() {
		if err != nil {
			panic(err)
		}
	}()

	conf := drv.NewNoOpsConfig()
	err = conf.SetOutputBucket(config.Aws.OutputBucket)
	if err != nil {
		return
	}
	err = conf.SetRegion(config.Aws.Region)
	if err != nil {
		return
	}
	if len(config.Aws.SessionTokenOnlyLocal) > 0 {
		err = conf.SetAccessID(config.Aws.AccessId)
		if err != nil {
			return
		}
		err = conf.SetSecretAccessKey(config.Aws.SecretAccessKey)
		if err != nil {
			return
		}
		conf.SetSessionToken(config.Aws.SessionTokenOnlyLocal)
	}

	db, err = sql.Open(drv.DriverName, conf.Stringify())
	if err != nil {
		return
	}

	clean.PushClose(func() (err error) {
		err = db.Close()
		return
	})

	return
}

func GetDB() *sql.DB {
	return db
}
