package github

import (
	"context"
	"net/http"

	"github.com/google/go-github/github"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"golang.org/x/oauth2"
)

var svc Service

func init() {
	svc = newService(
		&oauth2.Config{
			ClientID:     config.GitHub.ClientID,
			ClientSecret: config.GitHub.ClientSecret,
			Scopes:       []string{"user", "repo"},
			Endpoint: oauth2.Endpoint{
				AuthURL:  config.GitHub.Endpoint,
				TokenURL: config.GitHub.Endpoint,
			},
		},
	)
}

func ExchangeAccessToken(code string) (accessToken string, err error) {
	return svc.ExchangeAccessToken(code)
}

func GetGithubUser(accessToken string) (user *github.User, resp *github.Response, err error) {
	return svc.GetGithubUser(accessToken)
}

func GetLatestCommitId(auth *Auth, repoOwner, repo, branch string) (commitId string, err error) {
	return svc.GetLatestCommitId(auth, repoOwner, repo, branch)
}

func FetchSource(ctx context.Context, auth *Auth, repoOwner, repo, commitId, dst string, sizeLimit int64) (root string, err error) {
	return svc.FetchSource(ctx, auth, repoOwner, repo, commitId, dst, sizeLimit)
}

func ParseWebHook(r *http.Request) (body []byte, pushEvent *github.PushEvent, err error) {
	return svc.ParseWebHook(r)
}

func ValidateSignature(signature string, body, secretKey []byte) error {
	return svc.ValidateSignature(signature, body, secretKey)
}
