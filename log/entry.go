package log

import "github.com/sirupsen/logrus"

type Entry struct {
	oentry *logrus.Entry
}

func (entry *Entry) GetOEntry() *logrus.Entry {
	return entry.oentry
}

func (entry *Entry) IfErr(err error) {
	if err != nil {
		entry.Error(err)
	} else {
		entry.Info("Ok")
	}
}

func (entry *Entry) WithField(key string, val interface{}) *Entry {
	return &Entry{
		oentry: entry.oentry.WithField(key, val),
	}
}

func (entry *Entry) WithFields(fields Fields) *Entry {
	return &Entry{
		oentry: entry.oentry.WithFields(logrus.Fields(fields)),
	}
}

func (entry *Entry) Info(args ...interface{}) {
	entry.oentry.Info(args...)
}

func (entry *Entry) Infof(format string, args ...interface{}) {
	entry.oentry.Infof(format, args...)
}

func (entry *Entry) Error(args ...interface{}) {
	entry.oentry.Error(args...)
}

func (entry *Entry) Errorf(format string, args ...interface{}) {
	entry.oentry.Errorf(format, args...)
}

func (entry *Entry) Debug(args ...interface{}) {
	entry.oentry.Debug(args...)
}

func (entry *Entry) Debugf(format string, args ...interface{}) {
	entry.oentry.Debugf(format, args...)
}

func (entry *Entry) Warn(args ...interface{}) {
	entry.oentry.Warn(args...)
}

func (entry *Entry) Warnf(format string, args ...interface{}) {
	entry.oentry.Warnf(format, args...)
}

func (entry *Entry) Panic(args ...interface{}) {
	entry.oentry.Panic(args...)
}

func (entry *Entry) Panicf(format string, args ...interface{}) {
	entry.oentry.Panicf(format, args...)
}
