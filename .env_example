# Log
export LOG_LEVEL="info"

# DBRW
export DB_RW_USER="postgres"
export DB_RW_PASSWORD="123456"
export DB_RW_HOST="127.0.0.1"
export DB_RW_PORT="5432"
export DB_RW_NAME="postgres"
export DB_RW_MAX_OPEN_CONNS="32"
export DB_RW_MAX_CONN_LIFE_TIME="1h"
export DB_RW_MAX_CONN_IDLE_TIME="30m"

# DBRO
export DB_RO_USER="postgres"
export DB_RO_PASSWORD="123456"
export DB_RO_HOST="127.0.0.1"
export DB_RO_PORT="5432"
export DB_RO_NAME="postgres"
export DB_RO_MAX_OPEN_CONNS="32"
export DB_RO_MAX_CONN_LIFE_TIME="1h"
export DB_RO_MAX_CONN_IDLE_TIME="30m"

# RRedis
export REDIS_HOST="127.0.0.1:6379"
export REDIS_PASSWORD=""
export REDIS_NETWORK="tcp"
export REDIS_DB="0"
export REDIS_MAX_IDLE_CONN="32"
export REDIS_MAX_ACTIVE_CONN="32"
export REDIS_CONN_IDLE_TIMEOUT="1m"
export REDIS_CONN_WAIT="true"
export REDIS_MAX_LIFE_TIME="1m"

# API
export API_ADDR="0.0.0.0:56003"

# BTFS
export BTFS_HOST="http://gateway.btfs.io:5001"
export BTFS_UPLOAD_API_PATH="/api/v1/add"

# DNS
export DNS_ROOT_DOMAIN="on.bttscan.net"
export DNS_ZONE_ID="Z076524538CTIPKINFEGQ"

# Github App
export GITHUB_APP_ID="--github-app-id--"
export GITHUB_CLIENT_ID="--github-client-id--"
export GITHUB_CLIENT_SECRET="--github-secret---"
export GITHUB_PRIVATE_KEY_PATH=""
export GITHUB_ENDPOINT="https://github.com/login/oauth/access_token"

# Kafka
export KAFKA_BOOTSTRAP_SERVERS="127.0.0.1:9092"

# Deploy job
export DEPLOY_JOB_TOPIC="t-storage3-deploy"
export DEPLOY_JOB_GROUP_ID="g-storage3-deploy-v1.0"
export DEPLOY_JOB_OFFSET_RESET="earliest"
export DEPLOY_JOB_ROUTINES="2"

# Site name
SITE_NAME_RESERVED_WORDS="tron,justin,sunyuchen,justinsun,sunjustin,tronscan,justlend,apenft,btfs,trongrid,winklink,juststable,btt,trx,usdd,usdj,usdt,usdc,tusd,jst,wallet,scan"
SITE_NAME_RESERVED_WORDS_ALLOWED_BTTC_ADDRESSES=""
