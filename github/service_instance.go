package github

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/hex"
	"errors"
	"fmt"
	"hash"
	"io"
	"net/http"
	"net/url"
	"os"
	"path"
	"strings"
	"time"

	"github.com/google/go-github/github"
	"golang.org/x/oauth2"
)

const (
	// sha1Prefix is the prefix used by GitHub before the HMAC hexdigest.
	sha1Prefix = "sha1"
	// sha256Prefix and sha512Prefix are provided for future compatibility.
	sha256Prefix = "sha256"
	sha512Prefix = "sha512"

	// eventTypeHeader is the GitHub header key used to pass the event type.
	eventTypeHeader = "X-Github-Event"
	// deliveryIDHeader is the GitHub header key used to pass the unique ID for the webhook event.
	deliveryIDHeader = "X-Github-Delivery"
)

type service struct {
	oauthConfig *oauth2.Config
}

func newService(config *oauth2.Config) *service {
	return &service{
		oauthConfig: config,
	}
}

func (s *service) ExchangeAccessToken(code string) (accessToken string, err error) {
	oauthToken, err := s.oauthConfig.Exchange(context.Background(), code)
	if err != nil {
		return
	}
	accessToken = oauthToken.AccessToken
	return
}

func (s *service) GetGithubUser(accessToken string) (githubUser *github.User, resp *github.Response, err error) {
	cli := s.getGithubClient(accessToken)
	githubUser, _, err = cli.Users.Get(context.Background(), "")
	return
}

func (s *service) GetLatestCommitId(auth *Auth, repoOwner, repo, branch string) (commitId string, err error) {
	cli := s.getGithubClient(auth.OauthToken)
	if err != nil {
		return
	}
	commitDetail, _, err := cli.Repositories.GetCommit(
		context.Background(), repoOwner, repo, branch,
	)
	if err != nil {
		return
	}
	return *commitDetail.SHA, nil
}

func (s *service) ParseWebHook(r *http.Request) (body []byte, pushEvent *github.PushEvent, err error) {
	var payload []byte // Raw body that GitHub uses to calculate the signature.

	switch ct := r.Header.Get("Content-Type"); ct {
	case "application/json":
		var err error
		if body, err = io.ReadAll(r.Body); err != nil {
			return nil, nil, err
		}

		// If the content type is application/json,
		// the JSON payload is just the original body.
		payload = body

	case "application/x-www-form-urlencoded":
		// payloadFormParam is the name of the form parameter that the JSON payload
		// will be in if a webhook has its content type set to application/x-www-form-urlencoded.
		const payloadFormParam = "payload"

		var err error
		if body, err = io.ReadAll(r.Body); err != nil {
			return nil, nil, err
		}

		// If the content type is application/x-www-form-urlencoded,
		// the JSON payload will be under the "payload" form param.
		form, err := url.ParseQuery(string(body))
		if err != nil {
			return nil, nil, err
		}
		payload = []byte(form.Get(payloadFormParam))

	default:
		return nil, nil, fmt.Errorf("webhook request has unsupported Content-Type %q", ct)
	}
	event, err := github.ParseWebHook(github.WebHookType(r), payload)
	if err != nil {
		return nil, nil, err
	}
	switch event := event.(type) {
	case *github.PushEvent:
		return body, event, nil
	default:
		return nil, nil, fmt.Errorf("we just need push event, but the payload is: %T", event)
	}
}

func (s *service) FetchSource(ctx context.Context, auth *Auth, repoOnwer, repo, commitId, dst string, sizeLimit int64) (root string, err error) {
	cli := s.getGithubClient(auth.OauthToken)
	if err != nil {
		return
	}
	var opt = &github.RepositoryContentGetOptions{
		Ref: commitId,
	}
	url, _, err := cli.Repositories.GetArchiveLink(ctx, repoOnwer, repo, github.Tarball, opt)
	if err != nil {
		return
	}
	var client = &http.Client{Timeout: 5 * time.Minute}
	resp, err := client.Get(url.String())
	if err != nil || resp.StatusCode != http.StatusOK {
		return
	}
	defer resp.Body.Close()
	gr, err := gzip.NewReader(resp.Body)
	if err != nil {
		return
	}
	defer gr.Close()
	var (
		tr         = tar.NewReader(gr)
		hdr        *tar.Header
		accSize    int64
		isFirstDir = true
	)
	for {
		// 判断是已否取消
		select {
		case <-ctx.Done():
			err = ctx.Err()
			return
		default:
		}

		hdr, err = tr.Next()
		if err != nil {
			if err == io.EOF {
				err = nil
			}
			return
		}

		if hdr.FileInfo().IsDir() {
			// 取第一个（唯一一个）目录为代码的根目录（todo: 更可靠的实现)
			if isFirstDir {
				root = path.Join(dst, hdr.Name)
				isFirstDir = false
			}
			continue
		}

		// 限制下载的总文件大小
		size := hdr.FileInfo().Size()
		accSize += size
		if accSize > sizeLimit {
			err = fmt.Errorf("size overflow the limit %d", sizeLimit)
			return
		}

		var f *os.File
		filename := path.Join(dst, hdr.Name)
		f, err = s.createFile(filename)
		if err != nil {
			return
		}
		_, err = io.Copy(f, tr)
		f.Close()
		if err != nil {
			return
		}
	}
}

func (s *service) getGithubClient(accessToken string) (cli *github.Client) {
	cli = github.NewClient(oauth2.NewClient(
		context.Background(),
		oauth2.StaticTokenSource(
			&oauth2.Token{
				AccessToken: accessToken,
			},
		),
	))
	return
}

func (s *service) createFile(name string) (*os.File, error) {
	if !path.IsAbs(name) {
		return nil, fmt.Errorf("path must be absolute: %s", name)
	}
	dir := path.Dir(name)
	err := os.MkdirAll(dir, 0755)
	if err != nil {
		return nil, fmt.Errorf("failed to create directory: %w", err)
	}
	return os.Create(name)
}

// messageMAC returns the hex-decoded HMAC tag from the signature and its
// corresponding hash function.
func messageMAC(signature string) ([]byte, func() hash.Hash, error) {
	if signature == "" {
		return nil, nil, errors.New("missing signature")
	}
	sigParts := strings.SplitN(signature, "=", 2)
	if len(sigParts) != 2 {
		return nil, nil, fmt.Errorf("error parsing signature %q", signature)
	}

	var hashFunc func() hash.Hash
	switch sigParts[0] {
	case sha1Prefix:
		hashFunc = sha1.New
	case sha256Prefix:
		hashFunc = sha256.New
	case sha512Prefix:
		hashFunc = sha512.New
	default:
		return nil, nil, fmt.Errorf("unknown hash type prefix: %q", sigParts[0])
	}

	buf, err := hex.DecodeString(sigParts[1])
	if err != nil {
		return nil, nil, fmt.Errorf("error decoding signature %q: %v", signature, err)
	}
	return buf, hashFunc, nil
}

// genMAC generates the HMAC signature for a message provided the secret key
// and hashFunc.
func genMAC(message, key []byte, hashFunc func() hash.Hash) []byte {
	mac := hmac.New(hashFunc, key)
	mac.Write(message)
	return mac.Sum(nil)
}

// checkMAC reports whether messageMAC is a valid HMAC tag for message.
func checkMAC(message, messageMAC, key []byte, hashFunc func() hash.Hash) bool {
	expectedMAC := genMAC(message, key, hashFunc)
	return hmac.Equal(messageMAC, expectedMAC)
}

// validateSignature validates the signature for the given payload.
// signature is the GitHub hash signature delivered in the X-Hub-Signature header.
// payload is the JSON payload sent by GitHub Webhooks.
// secretKey is the GitHub Webhook secret message.
//
// GitHub API docs: https://developer.github.com/webhooks/securing/#validating-payloads-from-github
func (s *service) ValidateSignature(signature string, payload, secretKey []byte) error {
	messageMAC, hashFunc, err := messageMAC(signature)
	if err != nil {
		return err
	}
	if !checkMAC(payload, messageMAC, secretKey, hashFunc) {
		return errors.New("payload signature check failed")
	}
	return nil
}
