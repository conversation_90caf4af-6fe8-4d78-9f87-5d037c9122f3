package s3

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"io/ioutil"
	"net/http"
	"strings"
)

func HeadBucket(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}

	accessKey := getAccessKey(req.Header)
	bizInfo.AccessKey = accessKey

	return proxy.DoProxy(bizInfo, req)
}

func CreateBucket(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
		After:  parseCreateBucket,
	}

	accessKey := getAccessKey(req.Header)
	bizInfo.AccessKey = accessKey
	return proxy.DoProxy(bizInfo, req)
}

func ListBucket(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
		After:  parseListBucket,
	}

	accessKey := getAccessKey(req.Header)
	bizInfo.AccessKey = accessKey

	return proxy.DoProxy(bizInfo, req)
}

func DeleteBucket(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}

	bizInfo.AccessKey = getAccessKey(req.Header)

	return proxy.DoProxy(bizInfo, req)
}

func PutBucketACL(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}

	accessKey := getAccessKey(req.Header)
	bizInfo.AccessKey = accessKey

	return proxy.DoProxy(bizInfo, req)
}

func GetBucketACL(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}

	accessKey := getAccessKey(req.Header)
	bizInfo.AccessKey = accessKey

	return proxy.DoProxy(bizInfo, req)

}

func parseCreateBucket(bizInfo *s3.BizInfo, resp *http.Response) (*http.Response, error) {
	resp.Header.Set("Location", "/"+bizInfo.BucketName)
	return resp, nil
}

func parseListBucket(bizInfo *s3.BizInfo, resp *http.Response) (*http.Response, error) {

	type Bucket struct {
		CreationDate string `xml:"CreationDate"`
		Name         string `xml:"Name"`
	}

	type Owner struct {
		DisplayName string `xml:"DisplayName"`
		ID          string `xml:"ID"`
	}

	type ListAllMyBucketsResult struct {
		XMLName xml.Name `xml:"ListAllMyBucketsResult"`
		Buckets []Bucket `xml:"Buckets>Bucket"`
		Owner   Owner    `xml:"Owner"`
	}

	out := ListAllMyBucketsResult{}
	err := xml.NewDecoder(resp.Body).Decode(&out)
	if err != nil {
		return nil, err
	}

	for i, b := range out.Buckets {
		out.Buckets[i].Name = strings.TrimPrefix(b.Name, getbucketPrefix(bizInfo.AccessKey)+"-")
	}

	var buf bytes.Buffer
	buf.WriteString(xml.Header)
	enc := xml.NewEncoder(&buf)
	enc.Indent("", "  ")
	err = enc.Encode(out)
	if err != nil {
		fmt.Println("XML编码失败:", err)
		return resp, err
	}

	// 设置新的响应体
	resp.Header.Set("Content-Length", fmt.Sprintf("%d", buf.Len()))
	resp.Body = ioutil.NopCloser(&buf)

	return resp, nil
}
