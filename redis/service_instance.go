package redis

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	ErrLockBusy               = errors.New("lock is busy")
	ErrLockTimeoutSecsToSmall = errors.New("lock time out seconds is to small")
)

type service struct {
	rdb *redis.ClusterClient
}

type serviceOptions struct {
	Network         string
	Address         string
	Password        string
	DB              int
	MaxIdle         int
	MaxActive       int
	IdleTimeout     time.Duration
	Wait            bool
	MaxConnLifetime time.Duration
}

func newService(options *serviceOptions) (s *service, err error) {
	// Split the address string by comma to support multiple Redis cluster nodes
	addrs := strings.Split(options.Address, ",")
	
	// Clean up whitespace from addresses
	for i, addr := range addrs {
		addrs[i] = strings.TrimSpace(addr)
	}
	
	rdb := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        addrs,
		Password:     options.Password,
		// 添加集群重定向处理
		MaxRedirects:   8, // Increase redirects to better handle cluster relocations
		RouteByLatency: true,
		RouteRandomly:  false,
	})

	s = &service{
		rdb: rdb,
	}
	return
}

func (s *service) Close() (err error) {
	err = s.rdb.Close()
	return
}

func (s *service) SetNxEx(key, val string, timeoutSecs int) (ok bool, err error) {
	err = s.rdb.SetNX(context.Background(), key, val, time.Duration(timeoutSecs)*time.Second).Err()
	if err != nil {
		return
	}
	ok = true
	return
}

func (s *service) Del(key string) (n int64, err error) {
	n, err = s.rdb.Del(context.Background(), key).Result()
	return
}

func (s *service) Expire(key string, timeoutSecs int) (ok bool, err error) {
	ok, err = s.rdb.Expire(context.Background(), key, time.Duration(timeoutSecs)*time.Second).Result()
	return
}

// Lock 加分布式锁，retries为自旋次数，当锁繁忙时重试加锁的次数, 每次间隔时间1秒
// timeoutSecs锁的自动保持时间，加锁线程异常挂起或结束时，锁将在该时间内自动释放
// timeoutSecs为锁的保持时间，当锁未主动释放时，到达timeoutSecs时锁将过期
// 当加锁实例进程异常结束不能正常释放锁时，锁将在该时间内自动释放
// unlock为释放函数，捕获加锁环境，调用时将释放锁
func (s *service) Lock(key string, retries, timeoutSecs int) (unlock func() error, err error) {
	if timeoutSecs < 2 {
		err = ErrLockTimeoutSecsToSmall
		return
	}
	for i := 0; i < retries+1; i++ {
		var ok bool
		ok, err = s.SetNxEx(key, "1", timeoutSecs)
		if err != nil {
			return
		}
		if ok {
			unlockCh := make(chan struct{})
			errCh := make(chan error)
			go func() {
				select {
				case <-unlockCh:
					_, dErr := s.Del(key)
					errCh <- dErr
					return
				}
			}()
			unlock = func() error {
				close(unlockCh)
				uErr := <-errCh
				return uErr
			}
			return
		}
		time.Sleep(1 * time.Second)
	}
	err = ErrLockBusy
	return
}