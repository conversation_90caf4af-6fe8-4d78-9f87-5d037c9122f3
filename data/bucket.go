package data

import (
	"errors"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

const (
	PublicReadWrite   = "public-read-write"
	PublicRead        = "public-read"
	Private           = "private"
	AuthenticatedRead = "authenticated-read"
)

const defaultRegion = "us-east-1"

const (
	insertBucket = `insert into tb_bucket 
	(bucket_name, access_key, acl, region, location) values 
	($1, $2, $3, $4, $5)`

	updateBucketState = `UPDATE tb_bucket SET "is_deleted" = true WHERE "access_key" = $1 and "bucket_name" = $2;`

	updateBucketAcl = `UPDATE tb_bucket SET "acl" = $1 WHERE "access_key" = $2 and "bucket_name" = $3;`

	queryBucketList = `
		select id, bucket_name, access_key, acl, region, is_deleted, created_at, updated_at, location 
		from 
			tb_bucket
		where 
			is_deleted = false and access_key = $1
		order by 
			id desc`
	queryBucket = `select id, bucket_name, acl,access_key, region, is_deleted, created_at, updated_at, location from tb_bucket where access_key=$1 and bucket_name=$2 and is_deleted=false`
)

func InsertBucket(bucket *model.Bucket) error {
	if bucket.Acl == "" {
		bucket.Acl = PublicRead
	}
	if bucket.Region == "" {
		bucket.Region = defaultRegion
	}
	_, err := postgres.ExecByRW(insertBucket,
		bucket.BucketName, bucket.AccessKey, bucket.Acl, bucket.Region, bucket.Location)
	return err
}

func DeleteBucket(key, bucketName string) error {
	_, err := postgres.ExecByRW(updateBucketState, key, bucketName)
	return err
}

func UpdateBucketAcl(key, bucketName, acl string) error {
	_, err := postgres.ExecByRW(updateBucketAcl, acl, key, bucketName)
	return err
}

func ListBucket(key string) ([]*model.Bucket, error) {
	list := make([]*model.Bucket, 0)
	err := postgres.SelectByRO(queryBucketList, &list, key)
	return list, err
}

func GetBucket(key, bucketName string) (*model.Bucket, error) {
	bucket := &model.Bucket{}
	err := postgres.GetByRO(queryBucket, bucket, key, bucketName)
	if errors.Is(err, postgres.ErrNoRows) {
		return nil, nil
	}
	return bucket, err
}
