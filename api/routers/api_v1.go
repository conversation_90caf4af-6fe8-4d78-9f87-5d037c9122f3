package routers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/handlers"
	"gitlab.insidebt.net/btfs/storage3-backend/api/handlers/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/api/middlewares"
)

func registerApiV1(v1 *gin.RouterGroup) {
	v1.POST("/login", handlers.Login)
	v1.GET("/login", handlers.Login)
	v1.POST("/get_sign_nonce", handlers.GetUserSignNonce)
	v1.POST("/get_preset_build_setting_list", handlers.GetPresetBuildSettingList)

	// awesome, non-login state
	v1.POST("/check_site_name", handlers.CheckSiteName)
	v1.POST("/batch_get_site_info", handlers.BatchGetSiteInfo)
	v1.POST("/get_signed_deploy_list", handlers.GetSignedDeployList)
	v1.POST("/trigger_deploy_by_github_webhook", handlers.TriggerDeployByGithub)

	auth := v1.Group("/")
	auth.Use(middlewares.Auth())
	auth.GET("/github_callback", handlers.GithubCallback)
	auth.POST("/get_user_info", handlers.GetUserInfo)
	auth.POST("/logout", handlers.Logout)
	auth.POST("/disconnect_github", handlers.DisconnectGithub)

	// hosting
	auth.POST("/get_site_list", handlers.GetUserSiteList)
	auth.POST("/get_site_detail", handlers.GetUserSiteDetail)
	auth.POST("/update_site_name", handlers.UpdateUserSiteName)
	auth.POST("/update_site_setting", handlers.UpdateUserSiteSetting)
	auth.POST("/delete_site", handlers.DeleteUserSite)
	auth.POST("/get_deploy_list", handlers.GetUserDeployList)
	auth.POST("/get_deploy_detail", handlers.GetUserDeployDetail)
	auth.POST("/create_site_and_trigger_deploy", handlers.CreateUserSiteAndTriggerUserDeploy)
	auth.POST("/trigger_deploy", handlers.TriggerUserDeploy)
	auth.POST("/cancel_deploy", handlers.CancelUserDeploy)
	auth.POST("/get_profits_stat", handlers.GetUserProfitsStat)
	auth.POST("/update_deploy_signature", handlers.UpdateUserDeploySignature)
	auth.POST("/update_site_auto_deploy", handlers.UpdateSiteAutoDeploy)
	auth.POST("/regenerate_site_auto_deploy_secret", handlers.RegenerateSiteAutoDeploySecret)
	auth.POST("/check_user_address", handlers.CheckWhiteList)

	// daily stat
	v1.GET("/trigger_daily_stat", handlers.TriggerDailyStat)
	v1.GET("/get_daily_stat_list", handlers.GetDailyStatList)
	v1.GET("/get_site_stat_list", handlers.GetSiteStatList)

	// daily visit stat
	v1.POST("/get_site_daily_visit_stat_list", handlers.GetSiteDailyVisitStatList)

	// access key
	key := auth.Group("/key")
	key.POST("/generate", s3.GenerateKey)
	key.POST("/enable", s3.EnableKey)
	key.POST("/disable", s3.DisableKey)
	key.POST("/reset", s3.ResetKey)
	key.POST("/delete", s3.DeleteKey)
	key.POST("/detail", s3.DetailKey)
	key.POST("/list", s3.ListKeys)

	// download file
	v1.POST("cat", s3.DownloadObject)

	// bucket
	s3Prefix := Root.Group("/s3")
	bucket := s3Prefix.Group("/:bucket_name")

	bucket.HEAD("", s3.HeadBucket)
	bucket.HEAD("/", s3.HeadBucket)
	bucket.HEAD("/:object_name", s3.HeadObject)
	bucket.HEAD("/:object_name/*sub", s3.HeadObject)

	bucket.POST("/:object_name", s3.MultiPartUpload)
	bucket.POST("/:object_name/*sub", s3.MultiPartUpload)
	bucket.POST("", s3.DeleteObjects)
	bucket.POST("/", s3.DeleteObjects)

	bucket.PUT("", s3.CreateBucket)
	bucket.PUT("/", s3.CreateBucket)
	bucket.PUT("/:object_name", s3.PutObject)
	bucket.PUT("/:object_name/*sub", s3.PutObject)

	bucket.DELETE("", s3.DeleteBucket)
	bucket.DELETE("/", s3.DeleteBucket)
	bucket.DELETE("/:object_name", s3.DeleteObject)
	bucket.DELETE("/:object_name/*sub", s3.DeleteObject)

	s3Prefix.GET("", s3.ListBuckets)
	s3Prefix.GET("/", s3.ListBuckets)
	bucket.GET("", s3.ListObject)
	bucket.GET("/", s3.ListObject)
	bucket.GET("/:object_name", s3.GetObjectAcl)
	bucket.GET("/:object_name/*sub", s3.GetObjectAcl)

	bucket.OPTIONS("", func(ctx *gin.Context) { ctx.Writer.WriteHeader(http.StatusOK) })

	// site transfer
	auth.POST("/validate_address", handlers.CheckAddress)
	auth.POST("/transfer_site", handlers.TransferSite)
	auth.POST("/accept_or_decline_site", handlers.AcceptOrDeclineSite)
	// auth.POST("/get_transferring_site_list", handlers.GetTransferringSites)
	v1.POST("/get_site_transfer_history", handlers.SiteTransferHistory)
}
