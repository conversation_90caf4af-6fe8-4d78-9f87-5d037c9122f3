package docker

import (
	"context"
	"fmt"
	"github.com/docker/docker/api/types/mount"
	"path"
	"testing"
	"time"
)

func TestContainerRunAndCleanRealtime(t *testing.T) {
	var img = "fleek/create-react-app"
	var bsd = ""
	var wdr = path.Join("/project", bsd)
	var cmd = []string{"/bin/sh", "-c", "npm install && npm run build"}
	var mts = []Mount{{
		Type:          mount.TypeBind,
		Source:        "/Users/<USER>/Learn/demo",
		Target:        "/project",
		ReadOnly:      false,
		Consistency:   "",
		BindOptions:   nil,
		VolumeOptions: nil,
		TmpfsOptions:  nil,
	}}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	statusCode, err := ContainerRunAndClean(ctx, img, wdr, cmd, mts, func(line string) {
		fmt.Print(line + "\n")
	})
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(statusCode)
}
