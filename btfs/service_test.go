package btfs

import (
	"context"
	"encoding/json"
	"os"
	"path"
	"testing"
	"time"
)

func TestUploadItem(t *testing.T) {
	item := "/Users/<USER>/Learn/demo/package.json"
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	result, err := UploadItem(ctx, item, &UploadOptions{Pin: false})
	if err != nil {
		t.Fatal(err)
	}
	rawRst, _ := json.MarshalIndent(result, "", "  ")
	t.Log(string(rawRst))
}

func TestUploadDir(t *testing.T) {
	dir := "/Users/<USER>/Learn/test"
	results, err := UploadDir(context.Background(), dir, &UploadOptions{Pin: true})
	if err != nil {
		t.Fatal(err)
	}
	rawRst, _ := json.MarshalIndent(results, "", "  ")
	t.Log(string(rawRst))
}

func TestRemove(t *testing.T) {
	hash := "QmZfD5dxa3uP2kjodTXBfBj8p44RtEeLPvKJH4SEE21zYd"
	result, err := Remove(&RemoveOptions{Hash: hash})
	if err != nil {
		t.Fatal(err)
	}
	rawRst, _ := json.MarshalIndent(result, "", "  ")
	t.Log(string(rawRst))
}

func TestCreate(t *testing.T) {
	name := "/Users/<USER>/deploy-c517c20a-40e6-4203-9b07-a42c0a28acb6/imstevez-cool-go-09d1576/2021/images/W41_Go原生RPC与ARPC的简单使用/rpc_main.gif"
	dir := path.Dir(name)
	err := os.MkdirAll(dir, 0755)
	if err != nil {
		t.Fatal(err)
	}
	f, err := os.Create(name)
	defer f.Close()
	if err != nil {
		t.Fatal(err)
	}
	t.Log("ok")
}
