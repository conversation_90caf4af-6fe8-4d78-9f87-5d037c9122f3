package postgres

import (
	"context"

	"github.com/georgysavva/scany/pgxscan"
	"github.com/jackc/pgx/v4/log/logrusadapter"
	"github.com/jackc/pgx/v4/pgxpool"
	"gitlab.insidebt.net/btfs/storage3-backend/clean"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
)

var (
	rwPool, roPool, logPool *pgxpool.Pool
	scan                    *pgxscan.API
)

func init() {
	var err error

	dbscanAPI, err := pgxscan.NewDBScanAPI()
	if err != nil {
		log.Panic(err)
	}
	scan, err = pgxscan.NewAPI(
		dbscanAPI,
	)
	if err != nil {
		log.Panic(err)
	}

	rwConf, _ := pgxpool.ParseConfig("")
	rwConf.ConnConfig.Host = config.DBRW.Host
	rwConf.ConnConfig.Port = config.DBRW.Port
	rwConf.ConnConfig.Database = config.DBRW.Name
	rwConf.ConnConfig.User = config.DBRW.User
	rwConf.ConnConfig.Password = config.DBRW.Password
	rwConf.MaxConns = config.DBRW.MaxConns
	rwConf.MaxConnLifetime = config.DBRW.MaxConnLifeTime
	rwConf.MaxConnIdleTime = config.DBRW.MaxConnIdleTime
	rwConf.ConnConfig.Logger = logrusadapter.NewLogger(
		log.GetLogger("postgres_rw"),
	)
	rwPool, err = pgxpool.ConnectConfig(
		context.Background(), rwConf,
	)
	if err != nil {
		log.Panic(err)
	}
	clean.PushClose(func() error {
		rwPool.Close()
		return nil
	})

	roConf, _ := pgxpool.ParseConfig("")
	roConf.ConnConfig.Host = config.DBRO.Host
	roConf.ConnConfig.Port = config.DBRO.Port
	roConf.ConnConfig.Database = config.DBRO.Name
	roConf.ConnConfig.User = config.DBRO.User
	roConf.ConnConfig.Password = config.DBRO.Password
	roConf.MaxConns = config.DBRO.MaxConns
	roConf.MaxConnLifetime = config.DBRO.MaxConnLifeTime
	roConf.MaxConnIdleTime = config.DBRO.MaxConnIdleTime
	roConf.ConnConfig.Logger = logrusadapter.NewLogger(
		log.GetLogger("postgres_ro"),
	)
	roPool, err = pgxpool.ConnectConfig(
		context.Background(), roConf,
	)
	if err != nil {
		log.Panic(err)
	}
	clean.PushClose(func() error {
		roPool.Close()
		return nil
	})

	logConf, _ := pgxpool.ParseConfig("")
	logConf.ConnConfig.Host = config.DBRO.Host
	logConf.ConnConfig.Port = config.DBRO.Port
	logConf.ConnConfig.Database = config.DBRO.LogDBName
	logConf.ConnConfig.User = config.DBRO.User
	logConf.ConnConfig.Password = config.DBRO.Password
	logConf.MaxConns = config.DBRO.MaxConns
	logConf.MaxConnLifetime = config.DBRO.MaxConnLifeTime
	logConf.MaxConnIdleTime = config.DBRO.MaxConnIdleTime
	logConf.ConnConfig.Logger = logrusadapter.NewLogger(
		log.GetLogger("postgres_log"),
	)
	logPool, err = pgxpool.ConnectConfig(
		context.Background(), logConf,
	)
	if err != nil {
		log.Panic(err)
	}
	clean.PushClose(func() error {
		logPool.Close()
		return nil
	})
}
