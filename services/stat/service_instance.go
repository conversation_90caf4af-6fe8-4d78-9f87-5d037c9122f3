package stat

import (
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/redis"
	"time"
)

const statJobKey = "storage3_stat_job"

type service struct {
}

func (s *service) DoDailyStat() (err error) {
	var stat model.DailyStat

	defer func() {
		log.WithFields(log.Fields{
			"module": "daily_stat",
			"stat":   stat,
		}).IfErr(err)
	}()

	// 先加锁，任务可能被多个实例同时起动，同一时间仅一个实例执行即可
	unlock, err := redis.Lock(statJobKey, 0, 60*60)
	if err != nil {
		return
	}
	defer unlock()

	// 截止日期
	stat.StatDate = s.getStatDate()

	// 用户总量
	stat.UserTotal, err = data.GetUserTotal()
	if err != nil {
		return
	}

	// 站点总量
	stat.SiteTotal, err = data.GetAllUsersSiteTotal()
	if err != nil {
		return
	}

	// 站点资源总量
	stat.SiteSizeTotal, err = data.GetAllUsersSiteSizeTotal()
	if err != nil {
		return
	}

	// 站点点资源大于0的用户总量
	stat.SiteSizeUserTotal, err = data.GetSiteSizeUserTotalSql()
	if err != nil {
		return
	}

	// 统计时间
	now := time.Now()
	stat.CreatedAt = now
	stat.UpdatedAt = now

	err = data.UpsertDailyStatRecord(&stat)
	return
}

func (s *service) getStatDate() (statDate time.Time) {
	yesterdayTime := time.Now().Add(-1 * time.Hour)
	yesterdayDate := yesterdayTime.Format("2006-01-02")
	statDate, _ = time.Parse("2006-01-02", yesterdayDate)
	return
}

func (s *service) GetDailyStatList(arg *model.QueryDailyStatListArg) (list []*model.DailyStat, err error) {
	list, err = data.QueryDailyStatList(arg.From, arg.To)
	return
}

func (s *service) GetSiteStatList(arg *model.QuerySiteStatListArg) (list []*model.Site, err error) {
	list, err = data.QuerySiteStatList(arg.From, arg.To)
	return
}
