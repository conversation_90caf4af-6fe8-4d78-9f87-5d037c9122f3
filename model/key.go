package model

import "time"

type GenerateKeyResp struct {
	Key       string    `json:"key"`
	Secret    string    `json:"secret"`
	Enable    bool      `json:"enable"`
	IsDelete  bool      `json:"is_delete"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type KeyResponse struct {
	Id        int64     `json:"id"`
	UserId    string    `json:"user_id"`
	AccessKey string    `json:"access_key"`
	Secret    string    `json:"secret"`
	IsEnabled bool      `json:"is_enabled"`
	IsDeleted bool      `json:"is_deleted"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Key struct {
	Id        int64     `json:"id"`
	UserId    string    `json:"user_id"`
	AccessKey string    `json:"access_key"`
	Secret    string    `json:"secret"`
	Region    string    `json:"region"`
	IsEnabled bool      `json:"is_enabled"`
	IsDeleted bool      `json:"is_deleted"`
	Location  string    `json:"location"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ActiveKeyReq struct {
	Key string `json:"key"`
}

type DetailKeyReq struct {
	Key string `json:"key"`
}
