package cctx

import (
	"encoding/json"
	"errors"
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
)

const LoginUserKey = "xLoginUser"

var (
	ErrNotLogin = errors.New("not login")
)

func SetLoginUser(ctx *gin.Context, user *model.User) (err error) {
	userRaw, err := json.Marshal(user)
	if err != nil {
		return
	}
	session := sessions.Default(ctx)
	session.Set(LoginUserKey, string(userRaw))
	err = session.Save()
	return
}

func GetLoginUser(ctx *gin.Context) (user *model.User, err error) {
	session := sessions.Default(ctx)
	v, ok := session.Get(LoginUserKey).(string)
	if !ok {
		err = ErrNotLogin
		return
	}
	err = json.Unmarshal([]byte(v), &user)
	return
}

func GetLoginUserId(ctx *gin.Context) (userId string, err error) {
	user, err := GetLoginUser(ctx)
	if err != nil {
		return
	}
	userId = user.UserId
	return
}

func DeleteLoginUser(ctx *gin.Context) (err error) {
	session := sessions.Default(ctx)
	session.Delete(LoginUserKey)
	err = session.Save()
	return
}
