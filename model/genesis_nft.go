package model

import "time"

const (
	GenesisNFTMaterialGold   = "Gold"
	GenesisNFTMaterialSilver = "Silver"
	GenesisNFTMaterialBronze = "Bronze"
)

type GenesisNFT struct {
	Id             int64     `json:"id"`
	TokenId        string    `json:"token_id"`
	OwnerAddress   string    `json:"owner_address"`
	Material       string    `json:"material"`
	BlockNumber    int64     `json:"block_number"`
	BlockTimestamp int64     `json:"block_timestamp"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type UserGenesisNFTStatRecord struct {
	Material      string   `json:"material"`
	MaterialCount int64    `json:"material_count"`
	TokenIds      []string `json:"token_ids"`
}

type UserGenesisNFTStat struct {
	GoldCount      int64    `json:"gold_count"`
	GoldTokenIds   []string `json:"gold_token_ids"`
	SilverCount    int64    `json:"silver_count"`
	SilverTokenIds []string `json:"silver_token_ids"`
	BronzeCount    int64    `json:"bronze_count"`
	BronzeTokenIds []string `json:"bronze_token_ids"`
}

type UpdateGenesisNFTArg struct {
	TokenId        string `json:"token_id"`
	OwnerAddress   string `json:"owner_address"`
	BlockNumber    int64  `json:"block_number"`
	BlockTimestamp int64  `json:"block_timestamp"`
}

type InsertGenesisNFTArg struct {
	TokenId        string `json:"token_id"`
	OwnerAddress   string `json:"owner_address"`
	Material       string `json:"material"`
	BlockNumber    int64  `json:"block_number"`
	BlockTimestamp int64  `json:"block_timestamp"`
}
