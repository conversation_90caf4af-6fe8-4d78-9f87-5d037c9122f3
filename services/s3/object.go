package s3

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	aws "github.com/aws/aws-sdk-go/service/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"io/ioutil"

	"net/http"
	"net/url"
)

func AddObject(bizInfo *s3.BizInfo, request *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}
	return proxy.DoProxy(bizInfo, request)
}

func CopyObject(bizInfo *s3.BizInfo, request *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}
	return proxy.DoProxy(bizInfo, request)
}

func GetObject(biz *s3.BizInfo, request *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}

	accessKey := getAccessKey(request.Header)
	biz.AccessKey = accessKey

	return proxy.DoProxy(biz, request)
}

func ListObject(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {

	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
		// After:  parseListObject,
	}

	accessKey := getAccessKey(req.Header)
	bizInfo.AccessKey = accessKey

	return proxy.DoProxy(bizInfo, req)

}

func DeleteObject(bizInfo *s3.BizInfo, request *http.Request) (*http.Response, error) {

	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}

	return proxy.DoProxy(bizInfo, request)
}

func CreateMultiPartUpload(bizInfo *s3.BizInfo, request *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}
	return proxy.DoProxy(bizInfo, request)
}

func UploadMultiPart(bizInfo *s3.BizInfo, request *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}
	return proxy.DoProxy(bizInfo, request)
}

func CompleteMultiPartUpload(bizInfo *s3.BizInfo, request *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}
	return proxy.DoProxy(bizInfo, request)
}

func AbortMultiPartUpload(bizInfo *s3.BizInfo, req *http.Request) (*http.Response, error) {
	proxy := s3.BtfsProxy{
		Before: checkAndResetAuthorization,
	}
	return proxy.DoProxy(bizInfo, req)
}

func DownloadObject(ctx context.Context, cid string) (*http.Response, error) {
	args := url.Values{}
	args.Add("arg", cid)

	req := &s3.BTFSCmdRequest{
		Path:   config.BTFS.DownloadObject,
		Params: args,
	}

	client := s3.GetBTFSCmdClient(config.BTFS.VisitType)
	return client.PostProxy(ctx, req)
}

func parseListObject(bizInfo *s3.BizInfo, resp *http.Response) (*http.Response, error) {
	out := aws.ListObjectsOutput{}
	err := xml.NewDecoder(resp.Body).Decode(&out)
	if err != nil {
		return nil, err
	}
	out.Name = &bizInfo.BucketName

	var buf bytes.Buffer
	buf.WriteString(xml.Header)
	enc := xml.NewEncoder(&buf)
	enc.Indent("", "  ")
	err = enc.Encode(out)
	if err != nil {
		fmt.Println("XML编码失败:", err)
		return resp, err
	}

	// 设置新的响应体
	resp.Header.Set("Content-Length", fmt.Sprintf("%d", buf.Len()))
	resp.Body = ioutil.NopCloser(&buf)

	return resp, nil
}
