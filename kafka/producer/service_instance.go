package producer

import (
	"fmt"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"strings"
)

type service struct {
	producer *kafka.Producer
	over     chan struct{}
}

func newService(bootstrapServers []string) (s *service, err error) {
	producer, err := kafka.NewProducer(&kafka.ConfigMap{
		"bootstrap.servers":   strings.Join(bootstrapServers, ","),
		"api.version.request": "true",
		"linger.ms":           10,
		"retries":             30,
		"retry.backoff.ms":    1000,
		"acks":                "1",
	})
	if err != nil {
		return
	}
	s = &service{
		producer: producer,
		over:     make(chan struct{}),
	}
	return
}

func (s *service) Start() (err error) {
	go func() {
		s.logEvents()
		close(s.over)
	}()
	return
}

func (s *service) Close() (err error) {
	notFlushed := s.producer.Flush(15 * 1000)
	s.producer.Close()
	<-s.over
	if notFlushed > 0 {
		err = fmt.Errorf("%d messages not flushed", notFlushed)
	}
	return
}

func (s *service) Produce(topic string, key, value []byte) (err error) {
	msg := &kafka.Message{
		TopicPartition: kafka.TopicPartition{
			Partition: kafka.PartitionAny,
			Topic:     &topic,
		},
		Value: value,
		Key:   key,
	}
	err = s.producer.Produce(msg, nil)
	return
}

func (s *service) logEvents() {
	for e := range s.producer.Events() {
		switch ev := e.(type) {
		case *kafka.Message:
			log.WithFields(log.Fields{
				"module":   "kafka_producer",
				"type":     "produce_message",
				"position": ev.TopicPartition.String(),
				"key":      string(ev.Key),
				"value":    string(ev.Value),
			}).IfErr(ev.TopicPartition.Error)
		}
	}
}
