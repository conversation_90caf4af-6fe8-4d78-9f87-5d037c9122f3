package btfs

import "context"

type UploadOptions struct {
	Pin bool `json:"pin"`
}

type UploadItemResult struct {
	Hash string `json:"Hash"`
	Name string `json:"BucketName"`
	Size string `json:"Size"`
}

type RemoveOptions struct {
	Hash string `json:"hash"`
}

type RemoveResult struct {
	Strings []string `json:"Strings"`
}

type Service interface {
	UploadItem(ctx context.Context, item string, options *UploadOptions) (result *UploadItemResult, err error)
	UploadDir(ctx context.Context, dir string, options *UploadOptions) (results []*UploadItemResult, err error)
	Remove(options *RemoveOptions) (result *RemoveResult, err error)
}
