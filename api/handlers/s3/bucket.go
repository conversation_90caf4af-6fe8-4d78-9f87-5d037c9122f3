package s3

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	biz "gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	btfs "gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/services/s3"
	"net/http"
)

// bucket

const (
	BucketPath = "bucket_name"
	ObjectPath = "object_name"
	SubPath    = "sub"
)

// HeadBucket
// aws s3api head-bucket --bucket bucket0 --endpoint-url http://127.0.0.1:1501
func HeadBucket(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	bucketName := ctx.Param(BucketPath)
	bizInfo := &biz.BizInfo{BucketName: bucketName}

	resp, err := s3.HeadBucket(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}
	response.Proxy(ctx, resp)
}

// CreateBucket
// 1. aws s3api create-bucket --bucket bucket0 --acl public-read --endpoint-url http://127.0.0.1:1501
// PutBucketAcl
// 2. aws s3api put-bucket-acl --bucket mybucket10 --acl public-read --endpoint-url http://127.0.0.1:1501
func CreateBucket(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	params := ctx.Request.URL.Query()
	bucketName := ctx.Param(BucketPath)
	bizInfo := &biz.BizInfo{BucketName: bucketName}

	if params.Has("acl") {
		var resp *http.Response
		resp, err = s3.PutBucketACL(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, resp)
		return
	}

	resp, err := s3.CreateBucket(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}

	response.Proxy(ctx, resp)
}

// ListBuckets
// aws s3 ls --endpoint-url http://127.0.0.1:1501
// aws s3api list-buckets --endpoint-url http://127.0.0.1:1501
func ListBuckets(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	bizInfo := &biz.BizInfo{}
	resp, err := s3.ListBucket(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}

	response.Proxy(ctx, resp)
}

// DeleteBucket aws s3api delete-bucket --bucket mybucket1 --endpoint-url http://127.0.0.1:1501
func DeleteBucket(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	bucketName := ctx.Param(BucketPath)
	bizInfo := &biz.BizInfo{BucketName: bucketName}
	resp, err := s3.DeleteBucket(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}
	response.Proxy(ctx, resp)
}

func errorProcess(ctx *gin.Context, err error) {
	if errors.Is(err, btfs.SignError) {
		response.ProxyError(ctx, response.ErrSignatureDoesNotMatch)
		return
	}

	if errors.Is(err, s3.ErrAccessKeyIsValid) {
		response.ProxyError(ctx, response.ErrInvalidAccessKeyID)
		return
	}

	if errors.Is(err, s3.ErrAccessKeyIsDisabled) {
		response.ProxyError(ctx, response.ErrAccessKeyDisabled)
		return
	}

	if err != nil {
		response.ProxyError(ctx, response.ErrInternalError)
		return
	}
}
