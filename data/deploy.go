package data

import (
	"encoding/json"
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/kafka/producer"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
	"strings"
	"time"
)

const insertDeployRecordSql = `
insert into tb_deploy
	(
		deploy_id, user_id, site_id, 
		site_name, status, domain, 
		repo, branch, commit_id, 
		file_hash, file_size, build_image, 
		build_command, publish_directory, base_directory, 
		log, summary, created_at, 
		started_at, finished_at, updated_at,
	 	repo_owner
	)
values
	(
		$1, $2, $3,
		$4, $5, $6,
		$7, $8, $9,
		$10, $11, $12,
		$13, $14, $15,
		$16, $17, $18,
		$19, $20, $21,
	 	$22
	)
`

func InsertDeployRecord(deploy *model.Deploy) (err error) {
	summary, _ := json.Marshal(deploy.Summary)
	_, err = postgres.ExecByRW(
		insertDeployRecordSql,
		deploy.DeployId, deploy.UserId, deploy.SiteId,
		deploy.SiteName, deploy.Status, deploy.Domain,
		deploy.Repo, deploy.Branch, deploy.CommitId,
		deploy.FileHash, deploy.FileSize, deploy.BuildImage,
		deploy.BuildCommand, deploy.PublishDirectory, deploy.BaseDirectory,
		deploy.Log, summary, deploy.CreatedAt,
		deploy.StartedAt, deploy.FinishedAt, deploy.UpdatedAt,
		deploy.RepoOwner,
	)
	return
}

type DeployUpdatedFields struct {
	SiteName    *string
	Status      *model.DeployStatus
	Domain      *string
	FileHash    *string
	FileSize    *int64
	StartedAt   *time.Time
	FinishedAt  *time.Time
	LogContent  *string
	SummaryItem *model.DeploySummaryItem
}

const updateDeployRecordSql = `update tb_deploy set %s where %s`

func UpdateDeployRecord(siteId, deployId string, fromStatus *model.DeployStatus, fields *DeployUpdatedFields) (updated bool, err error) {
	var args []interface{}

	var sets []string
	if fields.SiteName != nil {
		args = append(args, *fields.SiteName)
		sets = append(sets, fmt.Sprintf(`site_name = $%d`, len(args)))
	}
	if fields.Status != nil {
		args = append(args, *fields.Status)
		sets = append(sets, fmt.Sprintf(`status = $%d`, len(args)))
	}
	if fields.Domain != nil {
		args = append(args, *fields.Domain)
		sets = append(sets, fmt.Sprintf(`domain = $%d`, len(args)))
	}
	if fields.FileHash != nil {
		args = append(args, *fields.FileHash)
		sets = append(sets, fmt.Sprintf(`file_hash = $%d`, len(args)))
	}
	if fields.FileSize != nil {
		args = append(args, *fields.FileSize)
		sets = append(sets, fmt.Sprintf(`file_size = $%d`, len(args)))
	}
	if fields.StartedAt != nil {
		args = append(args, *fields.StartedAt)
		sets = append(sets, fmt.Sprintf(`started_at = $%d`, len(args)))
	}
	if fields.FinishedAt != nil {
		args = append(args, *fields.FinishedAt)
		sets = append(sets, fmt.Sprintf(`finished_at = $%d`, len(args)))
	}
	if fields.LogContent != nil {
		args = append(args, *fields.LogContent)
		sets = append(sets, fmt.Sprintf(`log = (log || $%d)`, len(args)))
	}
	if fields.SummaryItem != nil {
		summaryRaw, _ := json.Marshal([]*model.DeploySummaryItem{fields.SummaryItem})
		args = append(args, summaryRaw)
		sets = append(sets, fmt.Sprintf(`summary = (summary || $%d)`, len(args)))
	}
	sets = append(sets, `updated_at = (now() at time zone 'utc')`)
	setStr := strings.Join(sets, ", ")

	var whes []string
	args = append(args, siteId)
	whes = append(whes, fmt.Sprintf(`site_id = $%d`, len(args)))
	args = append(args, deployId)
	whes = append(whes, fmt.Sprintf(`deploy_id = $%d`, len(args)))
	if fromStatus != nil {
		args = append(args, fromStatus)
		whes = append(whes, fmt.Sprintf(`status = $%d`, len(args)))
	}
	wheStr := strings.Join(whes, " and ")

	sqlStr := fmt.Sprintf(updateDeployRecordSql, setStr, wheStr)
	tg, err := postgres.ExecByRW(sqlStr, args...)
	if err != nil {
		return
	}
	updated = tg.RowsAffected() > 0
	return
}

const queryDeployRecordSql = `select deploy_id, user_id, site_id, 
	site_name, status, domain, 
	repo, branch, commit_id, 
	file_hash, file_size, build_image, 
	build_command, publish_directory, base_directory, 
	log, summary, created_at, 
	started_at, finished_at, updated_at, signature, signature_message, 
    repo_owner
from 
	tb_deploy 
where 
	user_id = $1 and site_id = $2 and 
	deploy_id = $3 
limit 1`

func QueryDeployRecord(userId, siteId, deployId string) (deploy *model.Deploy, err error) {
	deploy = &model.Deploy{}
	err = postgres.GetByRW(queryDeployRecordSql, deploy, userId, siteId, deployId)
	return
}

const queryDeployRecordByDeployIdSql = `select deploy_id, user_id, site_id, 
	site_name, status, domain, 
	repo, branch, commit_id, 
	file_hash, file_size, build_image, 
	build_command, publish_directory, base_directory, 
	log, summary, created_at, 
	started_at, finished_at, updated_at, signature, signature_message, 
    repo_owner
from 
	tb_deploy 
where 
	deploy_id = $1 
limit 1`

func QueryDeployRecordByDeployId(deployId string) (deploy *model.Deploy, err error) {
	deploy = &model.Deploy{}
	err = postgres.GetByRW(queryDeployRecordByDeployIdSql, deploy, deployId)
	return
}

const (
	queryDeployListSql = `select deploy_id, site_id, status, repo, branch, 
	commit_id, file_hash, file_size, created_at, started_at, finished_at, signature, signature_message, repo_owner 
from 
	tb_deploy 
where 
	user_id = $1 and site_id = $2 
order by 
	id desc 
limit $3 offset $4`

	queryDeployTotalSql = `select count(1) from tb_deploy where
	user_id = $1 and site_id = $2`
)

func QueryDeployList(userId, siteId string, limit, offset int64) (list []*model.DeployListItem, total int64, err error) {
	err = postgres.SelectByRW(queryDeployListSql, &list, userId, siteId, limit, offset)
	if err != nil {
		return
	}
	err = postgres.GetByRW(queryDeployTotalSql, &total, userId, siteId)
	return
}

const (
	queryDeployListNonLoginSql = `select deploy_id, user_id, site_id, site_name, domain, status, repo, repo_owner, branch, 
	commit_id, file_hash, created_at, started_at, finished_at, signature, signature_message  
from 
	tb_deploy 
where 
	site_id = $1 and length(signature) > 0 
order by 
	id desc 
limit $2 offset $3`

	queryDeployTotalNonLoginSql = `select count(1) from tb_deploy where 
	site_id = $1 and length(signature) > 0`
)

func GetSignedDeployList(siteId string, limit, offset int64) (list []*model.DeployListItemNonLogin, total int64, err error) {
	err = postgres.SelectByRW(queryDeployListNonLoginSql, &list, siteId, limit, offset)
	if err != nil {
		return
	}
	err = postgres.GetByRW(queryDeployTotalNonLoginSql, &total, siteId)
	return
}

func SendDoDeployMessage(message *model.DoDeployArg) (err error) {
	value, err := json.Marshal(message)
	if err != nil {
		return
	}
	key := []byte(message.SiteId)
	err = producer.Produce(config.DeployJob.Topic, key, value)
	return
}
