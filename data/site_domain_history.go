package data

import (
	"errors"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
	"time"
)

var siteDomainHistoryLastToTime, _ = time.Parse("2006-01-02", "2100-01-01")

const getSiteDomainHistoryLatestRecordSql = `select * from tb_site_domain_history where site_id = $1 order by id desc limit 1`

const updateSiteDomainHistoryToTimeSql = `update tb_site_domain_history set to_time = $1, updated_at = (now() at time zone 'utc') where id = $2`

const insertSiteDomainHistoryRecordSql = `insert into tb_site_domain_history (
	site_id, domain, from_time, to_time 
) values (
	  $1, $2, $3, $4
)`

func AddSiteDomainHistory(history *model.SiteDomainHistory) (err error) {
	err = postgres.BeginFuncByRW(func(tx postgres.Tx) (txErr error) {
		prevHistory := &model.SiteDomainHistory{}
		txErr = tx.Get(getSiteDomainHistoryLatestRecordSql, prevHistory, history.SiteId)
		if txErr != nil && !errors.Is(txErr, postgres.ErrNoRows) {
			return
		}
		if errors.Is(txErr, postgres.ErrNoRows) {
			txErr = nil
		} else {
			_, txErr = tx.Exec(updateSiteDomainHistoryToTimeSql, history.FromTime, prevHistory.Id)
			if txErr != nil {
				return
			}
		}
		_, txErr = tx.Exec(
			insertSiteDomainHistoryRecordSql,
			history.SiteId,
			history.Domain,
			history.FromTime,
			siteDomainHistoryLastToTime,
		)
		return
	})
	return
}

func StopSiteDomainHistory(siteId string, toTime time.Time) (err error) {
	err = postgres.BeginFuncByRW(func(tx postgres.Tx) (txErr error) {
		prevHistory := &model.SiteDomainHistory{}
		txErr = tx.Get(getSiteDomainHistoryLatestRecordSql, prevHistory, siteId)
		if txErr != nil {
			return
		}
		_, txErr = tx.Exec(updateSiteDomainHistoryToTimeSql, toTime, prevHistory.Id)
		return
	})
	return
}

const getSiteDomainHistoryListSql = `select  *
from tb_site_domain_history where from_time < $1 and to_time > $2 and id > $3
order by id asc limit $4`

func GetSiteDomainHistoryList(from, to time.Time, lastId int64, limit int) (list []*model.SiteDomainHistory, err error) {
	err = postgres.SelectByRW(getSiteDomainHistoryListSql, &list, to, from, lastId, limit)
	return
}
