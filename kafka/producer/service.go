package producer

import (
	"gitlab.insidebt.net/btfs/storage3-backend/clean"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var svc Service

func init() {
	var err error
	svc, err = newService(config.Kafka.BootstrapServers)
	if err != nil {
		panic(err)
	}
	err = svc.Start()
	if err != nil {
		panic(err)
	}
	clean.PushClose(svc.Close)
}

func Produce(topic string, key, value []byte) (err error) {
	return svc.Produce(topic, key, value)
}
