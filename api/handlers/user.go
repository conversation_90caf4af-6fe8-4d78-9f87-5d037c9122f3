package handlers

import (
	"errors"
	"strings"
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

func GetUserSignNonce(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	var arg model.NonceReq
	err = ctx.ShouldBind(&arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	err = validateAddress(arg.Address, arg.Source)
	if err != nil && errors.Is(err, ErrAddressInvalid) {
		response.ParamErr(ctx, err)
		return
	}

	// address store in lower case hex format in db
	if model.UserSource(arg.Source) == model.UserSourceTRON {
		arg.Address = tron.TronAddressToEtherAddress(arg.Address)
	}
	arg.Address = strings.ToLower(arg.Address)
	u, err := data.GetUserByAddr(arg.Address, arg.Source)
	if err != nil && !errors.Is(err, postgres.ErrNoRows) {
		response.InternalErr(ctx)
		return
	}

	if errors.Is(err, postgres.ErrNoRows) {
		err = nil
		u.UserId = uuid.New().String()
		u.Address = arg.Address
		u.Source = model.UserSource(arg.Source)
		u.CreatedAt = time.Now()
		u.UpdatedAt = time.Now()
		u.SignNonce = 1
		err = data.CreateUser(u)
		if err != nil {
			response.InternalErr(ctx)
			return
		}
	}

	response.DataOK(ctx, response.Data{
		"sign_message": data.GetSignNonceInfo(u.SignNonce, u.Source),
	})
}

func GetUserInfo(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	u, err := data.GetUserByAddr(user.Address, string(user.Source))
	if err != nil && !errors.Is(err, postgres.ErrNoRows) {
		response.InternalErr(ctx)
		return
	}

	if errors.Is(err, postgres.ErrNoRows) {
		response.NotFound(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"user": u,
	})
}
