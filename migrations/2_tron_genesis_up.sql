
-- 1.tb_genesis_nft
create table if not exists tb_genesis_nft
(
    id              bigserial primary key,
    token_id        character varying(128)      not null default ''::character varying,
    owner_address   character varying(128)      not null default ''::character varying,
    material        character varying(64)       not null default ''::character varying,
    block_number    bigint                      not null default '0'::bigint,
    block_timestamp bigint                      not null default '0'::bigint,
    created_at      timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at      timestamp without time zone not null default (now() at time zone 'utc'),
    unique (token_id)
    );

create index tb_genesis_nft_owner_material_idx on tb_genesis_nft (owner_address, material);
create index tb_genesis_nft_block_timestamp_idx on tb_genesis_nft (block_timestamp);


-- 2.user
ALTER TABLE tb_user ADD COLUMN source VARCHAR(16);
UPDATE tb_user SET source = 'BTTC';
ALTER TABLE tb_user RENAME COLUMN bttc_address TO address;
UPDATE tb_user SET address = LOWER(address);
ALTER TABLE tb_user DROP CONSTRAINT IF EXISTS tb_user_bttc_address_key;
CREATE UNIQUE INDEX tb_user_address_source_idx ON tb_user (address, source);


-- 3.tb_site\tb_deploy (check cid)
alter table tb_site
    add column current_signature varchar(512) default '' not null;

alter table tb_deploy
    add column signature varchar(512) default '' not null;

