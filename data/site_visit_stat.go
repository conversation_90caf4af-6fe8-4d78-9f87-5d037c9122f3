package data

import (
	"errors"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
	"time"
)

var oldestSiteVisitStatDate, _ = time.Parse("2006-01-02", "2023-07-01")

const getLastSiteVisitStatDateSql = `select stat_date from tb_site_visit_stat order by id desc limit 1;`

func GetLastSiteVisitStatDate() (date time.Time, err error) {
	type t struct {
		StatDate time.Time `json:"stat_date"`
	}
	var scanT t
	err = postgres.GetByRW(getLastSiteVisitStatDateSql, &scanT)
	if errors.Is(err, postgres.ErrNoRows) {
		date = oldestSiteVisitStatDate
		err = nil
		return
	}
	date = scanT.StatDate
	return
}

const insertSiteVisitStatRecordSql = `insert into tb_site_visit_stat (
    site_id, domain, pv, uv, bandwidth, stat_date, from_time, to_time
) values (
    $1, $2, $3, $4, $5, $6, $7, $8
) on conflict(
	site_id, stat_date, from_time
) do nothing;`

func InsertSiteVisitStatRecord(stat *model.SiteVisitStat) (err error) {
	_, err = postgres.ExecByRW(
		insertSiteVisitStatRecordSql,
		stat.SiteId,
		stat.Domain,
		stat.PV,
		stat.UV,
		stat.Bandwidth,
		stat.StatDate,
		stat.FromTime,
		stat.ToTime,
	)
	return
}
