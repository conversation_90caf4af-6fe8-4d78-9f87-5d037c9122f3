package jobs

import (
	"context"
	"github.com/go-co-op/gocron"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/redis"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
	"strings"
	"time"
)

const scanGenesisNFTJobKey = "storage3_stat_scan_genesis_nft_job"

func StartScanGenesisNFT(ctx context.Context) (err error) {
	s := gocron.NewScheduler(time.UTC)
	GenCollStatJob, err := s.Every(5*time.Minute).Do(doScanGenesisNFTTask, ctx)
	if err != nil {
		return
	}
	GenCollStatJob.SingletonMode()
	s.StartAsync()
	<-ctx.Done()
	s.Stop()
	return
}

func doScanGenesisNFTTask(ctx context.Context) (err error) {
	var lastBlockTimestamp int64
	log.WithFields(log.Fields{"module": "start_do_scan_genesis_nft_task"}).Info()
	defer func() {
		log.WithFields(log.Fields{
			"module":               "end_do_scan_genesis_nft_task",
			"last_block_timestamp": lastBlockTimestamp,
		}).IfErr(err)
	}()

	unlock, err := redis.Lock(scanGenesisNFTJobKey, 0, 60*60)
	if err != nil {
		return
	}
	defer unlock()
	lastBlockTimestamp, err = data.QueryGenesisNFTLastBlockTimestamp()
	if err != nil {
		return
	}
	err = handleGenesisNFTTransferEventsPages(ctx, lastBlockTimestamp)
	return
}

func handleGenesisNFTTransferEventsPages(ctx context.Context, lastBlockTimestamp int64) (err error) {
	fingerprint := ""
	for {
		err = ctx.Err()
		if err != nil {
			break
		}
		err = handleGenesisNFTTransferEventsPage(ctx, lastBlockTimestamp, &fingerprint)
		if err != nil || fingerprint == "" {
			break
		}
	}
	return
}

func handleGenesisNFTTransferEventsPage(ctx context.Context, lastBlockTimestamp int64, fingerprint *string) (err error) {
	eventsRsp, err := tron.GetNFTTransferEvents(ctx, &tron.GetNFTTransferEventsArg{
		ContractAddress:   data.GenesisNFTContractAddress,
		MinBlockTimestamp: lastBlockTimestamp,
		Fingerprint:       *fingerprint,
		Limit:             100,
	})
	if err != nil {
		return
	}

	err = handleGenesisNFTTransferEvents(ctx, eventsRsp.Events)
	if err != nil {
		return
	}

	*fingerprint = eventsRsp.FingerPrint

	return
}

func handleGenesisNFTTransferEvents(ctx context.Context, events []*tron.NFTTransferEventItem) (err error) {
	for _, event := range events {
		err = ctx.Err()
		if err != nil {
			break
		}
		err = handleGenesisNFTTransferEvent(ctx, event)
		if err != nil {
			break
		}
	}
	return
}

func handleGenesisNFTTransferEvent(ctx context.Context, event *tron.NFTTransferEventItem) (err error) {
	updated, err := data.UpdateGenesisNFT(&model.UpdateGenesisNFTArg{
		TokenId:        event.TokenId,
		OwnerAddress:   strings.ToLower(event.To),
		BlockNumber:    event.BlockNumber,
		BlockTimestamp: event.BlockTimestamp,
	})
	if err != nil || updated {
		return
	}

	rsp, err := tron.GetNFTTokenUri(ctx, &tron.GetNFTTokenUriArg{
		ContractAddress: data.GenesisNFTContractAddress,
		TokenId:         event.TokenId,
	})
	if err != nil {
		return
	}

	material, ok := data.QueryGenesisNFTMaterialByUri(rsp.TokenUri)
	if !ok {
		log.Errorf("unknown token uri <%s>: <%s>", event.TokenId, rsp.TokenUri)
		return
	}

	err = data.InsertGenesisNFT(&model.InsertGenesisNFTArg{
		TokenId:        event.TokenId,
		OwnerAddress:   strings.ToLower(event.To),
		Material:       material,
		BlockNumber:    event.BlockNumber,
		BlockTimestamp: event.BlockTimestamp,
	})

	return
}
