package consumer

import (
	"context"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"strings"
	"sync"
	"time"
)

type service struct {
	consumer *kafka.Consumer
}

func newService(bootstrapServers []string, topic, groupId, offsetReset string) (s *service, err error) {
	consumer, err := kafka.NewConsumer(&kafka.ConfigMap{
		"api.version.request":      "true",
		"enable.auto.offset.store": "false",
		"heartbeat.interval.ms":    1000,     // 2s
		"session.timeout.ms":       60000,    // 60s
		"max.poll.interval.ms":     86400000, // max
		"auto.offset.reset":        offsetReset,
		"bootstrap.servers":        strings.Join(bootstrapServers, ","),
		"group.id":                 groupId,
	})
	if err != nil {
		return
	}
	err = consumer.Subscribe(topic, nil)
	if err != nil {
		return
	}
	s = &service{
		consumer: consumer,
	}
	return
}

func (s *service) Consume(ctx context.Context, handler <PERSON><PERSON><PERSON><PERSON>, routines int) {
	wg := &sync.WaitGroup{}
	rt := make(chan struct{}, routines)
	for {
		select {
		default:
			event := s.consumer.Poll(800)
			if event == nil {
				continue
			}
			message, ok := event.(*kafka.Message)
			if !ok {
				continue
			}
			rt <- struct{}{}
			wg.Add(1)
			go func() {
				defer func() {
					wg.Done()
					<-rt
				}()
				start := time.Now()
				hErr := handler.HandleMessage(newMessage(message))
				_, _ = s.consumer.StoreMessage(message)
				s.logConsume(message, start, hErr)
			}()
		case <-ctx.Done():
			wg.Wait()
			_ = s.consumer.Close()
			return
		}
	}
}

func (s *service) logConsume(msg *kafka.Message, start time.Time, hErr error) {
	log.WithFields(log.Fields{
		"module":   "kafka_consumer",
		"type":     "consume_message",
		"position": msg.TopicPartition.String(),
		"key":      string(msg.Key),
		"value":    string(msg.Value),
		"elapsed":  time.Now().Sub(start).String(),
	}).IfErr(hErr)
}
