package jobs

import (
	"context"

	"golang.org/x/sync/errgroup"
)

func Run(ctx context.Context) (err error) {
	egp := errgroup.Group{}

	egp.Go(func() error {
		return StartDeployJob(ctx)
	})

	egp.Go(func() error {
		return StartDailyStatJob(ctx)
	})

	egp.Go(func() error {
		return StartScanGenesisNFT(ctx)
	})

	egp.Go(func() error {
		return StartDailyVisitStatJob(ctx)
	})

	egp.Go(func() error {
		return StartTransferSiteCheckJob(ctx)
	})

	err = egp.Wait()
	return
}
