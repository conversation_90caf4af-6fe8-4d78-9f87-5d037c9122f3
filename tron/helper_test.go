package tron

import (
	"strings"
	"testing"
)

func TestTronAddressToEtherAddress(t *testing.T) {
	tronAddr := "TQfujfp9LNU4yRYcAiXcLRmjDuVKQ8U3AJ"
	etherAddr := "******************************************"
	if strings.ToLower(TronAddressToEtherAddress(tronAddr)) != etherAddr {
		t.Fat<PERSON>("result not match")
	}
	t.Logf(etherAddr)
}

func TestBase58AddressToHexAddress(t *testing.T) {
	b58Addr := "TQfujfp9LNU4yRYcAiXcLRmjDuVKQ8U3AJ"
	hexAddr := "0x41a144672d68ae80d60cfb974a9e70469d2598816d"
	if strings.ToLower(Base58AddressToHexAddress(b58Addr)) != hexAddr {
		t.<PERSON><PERSON>("result not match")
	}
	t.Log("ok")
}

func TestETHAddressToBase58(t *testing.T) {
	b58Addr := "TQfujfp9LNU4yRYcAiXcLRmjDuVKQ8U3AJ"
	hexAddr := "41a144672d68ae80d60cfb974a9e70469d2598816d"
	if HexAddressToBase58Address(hexAddr) != b58Addr {
		t.Fatal("result not match")
	}
	t.Log("ok")
}
