package model

import "time"

type UserSource string

const (
	UserSourceBTTC UserSource = "BTTC"
	UserSourceTRON UserSource = "TRON"
)

type User struct {
	Id               int64      `json:"id"`
	UserId           string     `json:"user_id"`
	Source           UserSource `json:"source"`
	Address          string     `json:"address"`
	GithubOauthToken string     `json:"github_oauth_token"`
	GithubId         int64      `json:"github_id"`
	GithubLogin      string     `json:"github_login"`
	SignNonce        int        `json:"sign_nonce"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

type LoginReq struct {
	Signature string `json:"signature" form:"signature" binding:"required"`
	Address   string `json:"address" form:"address" binding:"required"`
	Source    string `json:"source" form:"source" binding:"required,oneof=BTTC TRON"`
}

type NonceReq struct {
	Address string `json:"address" form:"address" binding:"required"`
	Source  string `json:"source" form:"source" binding:"required,oneof=BTTC TRON"`
}
