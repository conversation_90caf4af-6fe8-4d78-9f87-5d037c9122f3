package data

import (
	"fmt"
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

const sqlQueryUserByUserId = `SELECT 
	* 
FROM 
	tb_user 
WHERE 
	user_id = $1 
LIMIT 
	1
`

const sqlQueryUserByAddr = `SELECT 
	* 
FROM 
	tb_user 
WHERE 
	address = $1 AND source = $2
LIMIT 
	1
`

const sqlQueryUserByGithubOwner = `SELECT 
	* 
FROM 
	tb_user
WHERE 
	github_login = $1 
LIMIT 
	1
`

const sqlInsertUser = `INSERT INTO 
	tb_user ("user_id", "address", "source", "github_oauth_token", "sign_nonce", "github_id", "github_login", "created_at", "updated_at")
VALUES 
	($1, $2, $3, $4, $5, $6, $7, $8, $9);`

const sqlUpdateUserSignNonce = `UPDATE tb_user SET "sign_nonce" = "sign_nonce" + 1, "updated_at" = $1 WHERE "address" = $2;`

const sqlUpdateUserInfo = `
UPDATE tb_user SET 
	"github_oauth_token" = $1, "github_id" = $2, "github_login" = $3
WHERE "address" = $4;`

const sqlQueryUserTotal = `SELECT 
	COUNT(1) 
FROM 
	tb_user
`

func GetUserByAddr(address, source string) (user *model.User, err error) {
	user = &model.User{}
	err = postgres.GetByRW(sqlQueryUserByAddr, user, address, source)
	return
}

func GetUserByUserId(userId string) (user *model.User, err error) {
	user = &model.User{}
	err = postgres.GetByRW(sqlQueryUserByUserId, user, userId)
	return
}

func GetUserByGithubOwner(owner string) (user *model.User, err error) {
	user = &model.User{}
	err = postgres.GetByRW(sqlQueryUserByGithubOwner, user, owner)
	return
}

func CreateUser(user *model.User) (err error) {
	_, err = postgres.ExecByRW(sqlInsertUser,
		user.UserId, user.Address, user.Source, user.GithubOauthToken, user.SignNonce, user.GithubId, user.GithubLogin, user.CreatedAt, user.UpdatedAt)
	return
}

func UpdateUserSignNonce(address string) (err error) {
	_, err = postgres.ExecByRW(sqlUpdateUserSignNonce, time.Now(), address)
	return
}

func UpdateUserInfo(address string, token string, id int64, loginName string) (err error) {
	_, err = postgres.ExecByRW(sqlUpdateUserInfo, token, id, loginName, address)
	return
}

func GetSignNonceInfo(nonce int, source model.UserSource) string {
	return fmt.Sprintf("Welcome to BTFS Storage3, the nonce is %d", nonce)
}

func GetUserTotal() (total int64, err error) {
	err = postgres.GetByRW(sqlQueryUserTotal, &total)
	return
}
