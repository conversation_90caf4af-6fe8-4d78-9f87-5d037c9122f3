package handlers

import (
	"github.com/stretchr/testify/assert"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
	"testing"
)

func TestTronsgm(t *testing.T) {
	addr := "TJuQa3zWp13AFQc9UbqzuqNMPh6ie3BXcR"
	sign := "0x4e783c4609ee83dae9ff303a3b6ec9e384284e16b1f2f5dae36bcd15d8f5f9a807fa5057ba52c4a8c4bf1f3c288c38043c0c01f36699c98313c11e398c548a2d1c"
	msg := "Welcome to BTFS Finder, the nonce is 66"
	addr = tron.TronAddressToEtherAddress(addr)
	err := tronLogin(addr, msg, sign)
	assert.Equal(t, err, nil)

	sign = "0x927cc009154dacde03cd99445f0b4c64960fd2d82ffe0c2eee840535163830f97996cb72d7b09686edfd09953eca2e8518336618a8983433dec29ac943ab37ed1b"
	msg = "Welcome to BTFS Finder, the nonce is 80"
	err = tronLogin(addr, msg, sign)
	assert.Equal(t, err, nil)
}
