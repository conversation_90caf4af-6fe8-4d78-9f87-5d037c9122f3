package routers

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/handlers"
	"gitlab.insidebt.net/btfs/storage3-backend/api/middlewares"
	_ "gitlab.insidebt.net/btfs/storage3-backend/api/validators"
)

var Root *gin.Engine

func init() {
	gin.SetMode(gin.ReleaseMode)

	Root = gin.New()

	Root.Use(middlewares.Recovery())
	Root.Use(middlewares.Log())
	Root.Use(middlewares.Cors())
	Root.Use(middlewares.Session())

	// ping
	Root.GET("/ping", handlers.Ping)

	// v1
	v1 := Root.Group("/api/v1")
	registerApiV1(v1)
}
