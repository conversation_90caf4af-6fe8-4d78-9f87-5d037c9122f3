package middlewares

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"time"
)

func Log() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		startTime := time.Now()
		ctx.Next()
		endTime := time.Now()
		latencyTime := endTime.Sub(startTime)
		err := cctx.GetErr(ctx)
		log.WithFields(log.Fields{
			"module":      "api_request",
			"status_code": ctx.Writer.Status(),
			"latency_ms":  latencyTime.Milliseconds(),
			"client_id":   ctx.ClientIP(),
			"method":      ctx.Request.Method,
			"uri":         ctx.Request.RequestURI,
		}).IfErr(err)
	}
}
