package hosting

import (
	"gitlab.insidebt.net/btfs/storage3-backend/model"
)

type Service interface {
	QueryUserSiteList(user *model.User, transferringSiteList []string, arg *model.QuerySiteListArg) (list []*model.Site, total int64, err error)
	QueryUserSiteDetail(user *model.User, arg *model.QuerySiteDetailArg) (detail *model.Site, err error)
	UpdateUserSiteName(user *model.User, arg *model.UpdateSiteNameArg) (err error)
	UpdateUserSiteSetting(user *model.User, arg *model.UpdateSiteSettingArg) (err error)
	DeleteUserSite(user *model.User, arg *model.DeleteSiteArg) (err error)
	QueryUserDeployList(user *model.User, arg *model.QueryDeployListArg) (list []*model.DeployListItem, total int64, err error)
	GetSignedDeployList(arg *model.QueryDeployListArg) (list []*model.DeployListItemNonLogin, total int64, err error)
	QueryUserDeployDetail(user *model.User, arg *model.QueryDeployDetailArg) (detail *model.Deploy, err error)
	CreateUserSiteAndTriggerUserDeploy(user *model.User, arg *model.CreateSiteArg) (siteId, deployId string, err error)
	TriggerUserDeploy(user *model.User, arg *model.TriggerDeployArg) (siteId, deployId string, err error)
	DoDeploy(arg *model.DoDeployArg) (err error)
	CancelUserDeploy(user *model.User, arg *model.CancelDeployArg) (err error)
	QueryPresetBuildSettingList() (list []*model.PresetBuildSetting, total int64, err error)
	UpdateUserDeploySignature(user *model.User, arg *model.UpdateDeploySignatureArg) (err error)
	UpdateSiteAutoDeploy(user *model.User, arg *model.UpdateSiteAutoDeployArg) (err error)
	RegenerateSiteAutoDeploySecret(user *model.User, arg *model.RegenerateSiteAutoDeploySecretArg) (err error)

	QuerySiteDetailBySiteName(arg *model.QuerySiteDetailBySiteNameArg) (detail *model.Site, err error)
	QuerySiteDetailByRepo(arg *model.QuerySiteDetailByRepoArg) (detail []*model.Site, err error)
	QuerySiteDetailsBySiteNames(siteNames []string) (details []*model.Site, err error)
	QuerySiteDetailsBySiteIds(siteIds []string) (details []*model.Site, err error)
	QueryUserDeployDetailByDeployId(deployId string) (detail *model.Deploy, err error)

	GetSiteLastVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error)
	GetSite7daysVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error)
	GetSiteLastNVisitDaysStatList(arg *model.GetSiteDailyVisitStatListArg) (list []*model.RespSiteDailyVisitStatList, err error)

	CheckUserInWhiteList(user *model.User) error

	CheckBuildImageAndCommand(image, command string) error

	// site transfer

	TransferSite(user *model.User, arg *model.TransferSiteArg) error
	AcceptOrDeclineSite(user *model.User, arg *model.TransferSiteOpArg) error
	GetTransferringSite(user *model.User) (sites []*model.SiteTransferRecord, err error)
	GetTransferPendingSite(user *model.User) (sites []*model.SiteTransferRecord, err error)
	GetSiteTransferHistory(siteId string) (sites []*model.SiteTransferRecord, err error)
	GetTransferringSiteBySiteId(siteId string, user *model.User, isLatest bool) (site *model.SiteTransferRecord, err error)
	GetTransferAcceptedSiteBySiteIds(siteIds []string, userId string) (site []*model.SiteTransferRecord, err error)
}
