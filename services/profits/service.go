package profits

import (
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
	"strings"
)

type UserProfitsStat struct {
	IsSuperUser        bool                      `json:"is_super_user"`
	GenesisNFTStats    *model.UserGenesisNFTStat `json:"genesis_nft_stats"`
	UsedStoreSize      int64                     `json:"used_store_size"`
	UsedSiteNum        int                       `json:"used_site_num"`
	FreeStoreSize      int64                     `json:"free_store_size"`
	FreeSiteSize       int64                     `json:"free_site_size"`
	FreeSiteNum        int                       `json:"free_site_num"`
	NFTHolderStoreSize int64                     `json:"additional_store_size"`
	NFTHolderSiteSize  int64                     `json:"additional_site_size"`
	NFTHolderSiteNum   int                       `json:"additional_site_num"`
	TotalStoreSize     int64                     `json:"total_store_size"`
	TotalSiteSize      int64                     `json:"total_site_size"`
	TotalSiteNum       int                       `json:"total_site_num"`
}

func GetUserProfitsStat(user *model.User) (stat *UserProfitsStat, err error) {
	stat = &UserProfitsStat{}

	// super user
	address := user.Address
	if user.Source == model.UserSourceTRON {
		address = tron.HexAddressToBase58Address(address)
	}
	source, ok := config.Profits.SuperUserAddresses[address]
	stat.IsSuperUser = ok && strings.ToUpper(source) == string(user.Source)

	// used profits
	userStat, err := data.GetUserSiteStats(user.UserId)
	if err != nil {
		return
	}
	stat.UsedStoreSize = userStat.UserTotalFileSize
	stat.UsedSiteNum = userStat.UserTotalSite

	// free profits
	stat.FreeStoreSize = int64(config.Profits.FreeStoreSize)
	stat.FreeSiteSize = int64(config.Profits.FreeSiteSize)
	stat.FreeSiteNum = config.Profits.FreeSiteNum

	// nft holder profits
	if user.Source == model.UserSourceTRON {
		stat.GenesisNFTStats, err = data.QueryUserGenesisNFTStat(user.Address)
		if err != nil {
			return
		}
		switch getPriorityMaterial(stat.GenesisNFTStats) {
		case model.GenesisNFTMaterialGold:
			stat.NFTHolderStoreSize = int64(config.Profits.GoldNFTHolderStoreSize)
			stat.NFTHolderSiteSize = int64(config.Profits.GoldNFTHolderSiteSize)
			stat.NFTHolderSiteNum = config.Profits.GoldNFTHolderSiteNum
		case model.GenesisNFTMaterialSilver:
			stat.NFTHolderStoreSize = int64(config.Profits.SilverNFTHolderStoreSize)
			stat.NFTHolderSiteSize = int64(config.Profits.SilverNFTHolderSiteSize)
			stat.NFTHolderSiteNum = config.Profits.SilverNFTHolderSiteNum
		case model.GenesisNFTMaterialBronze:
			stat.NFTHolderStoreSize = int64(config.Profits.BronzeNFTHolderStoreSize)
			stat.NFTHolderSiteSize = int64(config.Profits.BronzeNFTHolderSiteSize)
			stat.NFTHolderSiteNum = config.Profits.BronzeNFTHolderSiteNum
		}
	}

	// total
	stat.TotalStoreSize = stat.FreeStoreSize + stat.NFTHolderStoreSize
	stat.TotalSiteSize += stat.FreeSiteSize + stat.NFTHolderSiteSize
	stat.TotalSiteNum += stat.FreeSiteNum + stat.NFTHolderSiteNum

	return
}

func getPriorityMaterial(stat *model.UserGenesisNFTStat) string {
	if stat.GoldCount > 0 {
		return model.GenesisNFTMaterialGold
	}
	if stat.SilverCount > 0 {
		return model.GenesisNFTMaterialSilver
	}
	if stat.BronzeCount > 0 {
		return model.GenesisNFTMaterialBronze
	}
	return ""
}
