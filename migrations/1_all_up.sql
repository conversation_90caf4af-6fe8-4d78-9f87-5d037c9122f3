create table if not exists tb_site
(
    id                         bigserial primary key,
    site_id                    character varying(128)      not null default ''::character varying,
    user_id                    character varying(128)      not null default ''::character varying,
    site_name                  character varying(64)       not null default ''::character varying,
    domain                     character varying(128)      not null default ''::character varying,
    repo_owner                 character varying(512)      not null default ''::character varying,
    repo                       character varying(512)      not null default ''::character varying,
    branch                     character varying(128)      not null default ''::character varying,
    build_image                character varying(128)      not null default ''::character varying,
    build_command              character varying(512)      not null default ''::character varying,
    publish_directory          character varying(128)      not null default ''::character varying,
    base_directory             character varying(128)      not null default ''::character varying,
    latest_published_deploy_id character varying(128)      not null default ''::character varying,
    current_file_hash          character varying(128)      not null default ''::character varying,
    current_file_size          bigint                      not null default '0'::bigint,
    latest_published_at        timestamp without time zone not null default (now() at time zone 'utc'),
    is_deleted                 smallint                    not null default '0'::smallint,
    created_at                 timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at                 timestamp without time zone not null default (now() at time zone 'utc'),
    unique (site_name),
    unique (site_id)
);


create table if not exists tb_deploy
(
    id                bigserial primary key,
    deploy_id         character varying(128)      not null default ''::character varying,
    user_id           character varying(128)      not null default ''::character varying,
    site_id           character varying(128)      not null default ''::character varying,
    site_name         character varying(64)       not null default ''::character varying,
    status            smallint                    not null default '0'::smallint,
    domain            character varying(128)      not null default ''::character varying,
    repo_owner        character varying(512)      not null default ''::character varying,
    repo              character varying(512)      not null default ''::character varying,
    branch            character varying(128)      not null default ''::character varying,
    commit_id         character varying(128)      not null default ''::character varying,
    file_hash         character varying(128)      not null default ''::character varying,
    file_size         bigint                      not null default '0'::bigint,
    build_image       character varying(128)      not null default ''::character varying,
    build_command     character varying(512)      not null default ''::character varying,
    publish_directory character varying(128)      not null default ''::character varying,
    base_directory    character varying(128)      not null default ''::character varying,
    log               text,
    summary           jsonb                       not null default 'null'::jsonb,
    created_at        timestamp without time zone not null default (now() at time zone 'utc'),
    started_at        timestamp without time zone not null default (now() at time zone 'utc'),
    finished_at       timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at        timestamp without time zone not null default (now() at time zone 'utc'),
    unique (deploy_id)
);

CREATE TABLE IF NOT EXISTS tb_user
(
    id                 BIGSERIAL PRIMARY KEY,
    user_id            CHARACTER VARYING NOT NULL DEFAULT '',
    bttc_address       CHARACTER VARYING NOT NULL DEFAULT '',
    github_oauth_token CHARACTER VARYING NOT NULL DEFAULT '',
    github_id          bigint            not null default '0'::bigint,
    github_login       CHARACTER VARYING NOT NULL DEFAULT '',
    sign_nonce         int8,
    created_at         timestamptz(6)    not null default (now() at time zone 'utc'),
    updated_at         timestamptz(6)    not null default (now() at time zone 'utc'),
    unique (user_id),
    unique (bttc_address)
);
CREATE INDEX tb_user_github_login_idx ON tb_user (github_login);

create table if not exists tb_daily_stat
(
    id                   bigserial primary key,
    stat_date            timestamp without time zone not null default (now() at time zone 'utc'),
    user_total           bigint                      not null default '0'::bigint,
    site_total           bigint                      not null default '0'::bigint,
    site_size_total      bigint                      not null default '0'::bigint,
    site_size_user_total bigint                      not null default '0'::bigint,
    created_at           timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at           timestamp without time zone not null default (now() at time zone 'utc'),
    unique (stat_date)
);
