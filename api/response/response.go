package response

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/services/hosting"
	"net/http"
)

type Data map[string]interface{}

type Response struct {
	Code    Code   `json:"code"`
	Message string `json:"message"`
	Data    Data   `json:"data"`
}

func OK(ctx *gin.Context) {
	ctx.JSON(
		http.StatusOK,
		&Response{
			Code:    CodeOK,
			Message: "ok",
			Data:    nil,
		},
	)
}

func DataOK(ctx *gin.Context, data Data) {
	ctx.JSON(
		http.StatusOK,
		&Response{
			Code:    CodeOK,
			Message: "ok",
			Data:    data,
		},
	)
}

func CustomOK(ctx *gin.Context, resp *Response) {
	ctx.JSON(http.StatusOK, resp)
}

func ParamErr(ctx *gin.Context, err error) {
	ctx.JSON(
		http.StatusOK,
		&Response{
			Code:    CodeParamErr,
			Message: "param err: " + err.Error(),
			Data:    nil,
		},
	)
}

func NotFound(ctx *gin.Context) {
	ctx.JSON(
		http.StatusOK,
		&Response{
			Code:    CodeNotFound,
			Message: "not found",
			Data:    nil,
		},
	)
}

func InternalErr(ctx *gin.Context) {
	ctx.JSON(
		http.StatusOK,
		&Response{
			Code:    CodeInternalErr,
			Message: "internal err",
			Data:    nil,
		},
	)
}

func UnAuthorizedErr(ctx *gin.Context) {
	ctx.AbortWithStatusJSON(
		http.StatusOK,
		&Response{
			Code:    CodeUnauthorizedErr,
			Message: "you need to authorize",
			Data:    nil,
		},
	)
}

func BadRequestErr(ctx *gin.Context, code Code, err error) {
	ctx.JSON(
		http.StatusOK,
		&Response{
			Code:    code,
			Message: err.Error(),
			Data:    nil,
		},
	)
}

func HostingOperationErr(ctx *gin.Context, err error) {
	switch err {
	case data.ErrNotFound:
		NotFound(ctx)
	case hosting.ErrSiteBusy:
		BadRequestErr(ctx, CodeSiteBusy, err)
	case hosting.ErrSiteDeleted:
		BadRequestErr(ctx, CodeSiteDeleted, err)
	case hosting.ErrSiteNameExists:
		BadRequestErr(ctx, CodeSiteNameExists, err)
	case hosting.ErrDeployHasFinished:
		BadRequestErr(ctx, CodeDeployHasFinished, err)
	case hosting.ErrSiteNameReserved:
		BadRequestErr(ctx, CodeSiteNameReserved, err)
	case hosting.ErrSiteNumInsufficient:
		BadRequestErr(ctx, CodeSiteNumInsufficient, err)
	case hosting.ErrOperationBusy:
		BadRequestErr(ctx, CodeOperationBusy, err)
	default:
		InternalErr(ctx)
	}
}
