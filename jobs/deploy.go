package jobs

import (
	"context"

	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/kafka/consumer"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/services/hosting"
)

func StartDeployJob(ctx context.Context) (err error) {
	consumerService, err := consumer.NewService(
		config.DeployJob.Topic, config.DeployJob.GroupId, config.DeployJob.OffsetReset,
	)
	if err != nil {
		return
	}
	consumerService.Consume(ctx, new(deployConsumeHandler), config.DeployJob.Routines)
	return
}

type deployConsumeHandler struct{}

func (h *deployConsumeHandler) HandleMessage(message *consumer.Message) (err error) {
	arg := &model.DoDeployArg{}
	err = message.Decode(arg)
	if err != nil {
		return
	}
	log.WithFields(log.Fields{
		"module":    "deploy_job",
		"type":      "start_deploy",
		"user_id":   arg.UserId,
		"site_id":   arg.SiteId,
		"deploy_id": arg.DeployId,
	}).Info()
	err = hosting.DoDeploy(arg)
	return
}
