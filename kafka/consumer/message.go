package consumer

import (
	"bytes"
	"encoding/json"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

type MessageHandler interface {
	HandleMessage(message *Message) error
}

type Message struct {
	message *kafka.Message
}

func newMessage(message *kafka.Message) *Message {
	return &Message{
		message: message,
	}
}

func (m *Message) Decode(dst interface{}) error {
	return json.NewDecoder(bytes.NewReader(m.message.Value)).Decode(dst)
}

func (m *Message) String() string {
	return m.message.String()
}

func (m *Message) Key() string {
	return string(m.message.Key)
}

func (m *Message) Value() string {
	return string(m.message.Value)
}
