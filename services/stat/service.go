package stat

import "gitlab.insidebt.net/btfs/storage3-backend/model"

var svc Service

func init() {
	svc = &service{}
}

func DoDailyStat() (err error) {
	return svc.DoDailyStat()
}

func GetDailyStatList(arg *model.QueryDailyStatListArg) (list []*model.DailyStat, err error) {
	return svc.GetDailyStatList(arg)
}

func GetSiteStatList(arg *model.QuerySiteStatListArg) (list []*model.Site, err error) {
	return svc.GetSiteStatList(arg)
}
