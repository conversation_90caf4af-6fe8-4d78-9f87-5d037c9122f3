package response

import "net/http"

type Code int

const (
	CodeOK Code = iota
	CodeNotFound
	CodeParamErr
	CodeInternalErr
	CodeUnauthorizedErr
	CodeBadRequest
	CodeKeyError = 6

	CodeSiteDeleted         = 1001 // 触发部署时，取消部署时，修改站点名称时可能出现，站点已被删除
	CodeSiteNameExists      = 1002 // 修改站点名称时，站点名称已存在
	CodeSiteNameReserved    = 1003 // 修改站点名称时，站点名称包含有保留字
	CodeSiteBusy            = 1004 // 触发部署时，取消部署时，修改站点名称时可能出现，站点正在执行解析，无法执行操作
	CodeDeployHasFinished   = 1005 // 取消部署时，对应部署已结束（完成，失败或取消）
	CodeSiteNumInsufficient = 1006 // 创建站点并触发部署时，站点数量已用尽
	CodeOperationBusy       = 1007 // 创建站点时同时，创建多个
	CodeNotAllowedToDeploy  = 1008 // 不在白名单用户，不能部署
	CodeAddressInvalid      = 1009
	CodeAddressNotExits     = 1010
	CodeInvalidSignature    = 1011
)

var (
	ErrBucketAlreadyExists = &Error{
		code:           "BucketAlreadyExists",
		description:    "The requested bucket name is not available. The bucket namespace is shared by all users of the system. Please select a different name and try again.",
		httpStatusCode: http.StatusConflict,
	}

	ErrAccessDenied = &Error{
		code:           "AccessDenied",
		description:    "Access Denied.",
		httpStatusCode: http.StatusForbidden,
	}

	ErrNoSuchBucket = &Error{
		code:           "NoSuchBucket",
		description:    "The specified bucket does not exist",
		httpStatusCode: http.StatusNotFound,
	}

	ErrInternalError = &Error{
		code:           "InternalError",
		description:    "We encountered an internal error, please try again.",
		httpStatusCode: http.StatusInternalServerError,
	}

	ErrNoSuchKey = &Error{
		code:           "NoSuchKey",
		description:    "The specified key does not exist.",
		httpStatusCode: http.StatusNotFound,
	}

	ErrInvalidAccessKeyID = &Error{
		code:           "InvalidAccessKeyId",
		description:    "The Access Key Id you provided does not exist in our records.",
		httpStatusCode: http.StatusForbidden,
	}

	ErrAccessKeyDisabled = &Error{
		code:           "InvalidAccessKeyId",
		description:    "Your account is disabled; please contact your administrator.",
		httpStatusCode: http.StatusForbidden,
	}

	ErrSignatureDoesNotMatch = &Error{
		code:           "SignatureDoesNotMatch",
		description:    "The request signature we calculated does not match the signature you provided. Check your key and signing method.",
		httpStatusCode: http.StatusForbidden,
	}
)
