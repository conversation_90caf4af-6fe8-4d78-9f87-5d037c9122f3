package middlewares

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
)

func Auth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		_, err := cctx.GetLoginUser(ctx)
		if err == cctx.ErrNotLogin {
			response.UnAuthorizedErr(ctx)
			ctx.Abort()
			return
		}
		if err != nil {
			response.InternalErr(ctx)
			ctx.Abort()
			return
		}
		ctx.Next()
	}
}
