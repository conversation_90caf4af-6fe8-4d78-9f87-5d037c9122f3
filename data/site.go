package data

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

const insertSiteRecordSql = `
insert into tb_site
	(
		site_id, user_id, site_name,  
		domain, repo, branch,
		build_image, build_command, publish_directory, 
		base_directory, latest_published_deploy_id, current_file_hash, 
		current_file_size, latest_published_at, is_deleted,
		created_at, updated_at, repo_owner
	)
values
	(
		$1, $2, $3,
		$4, $5, $6,
		$7, $8, $9,
		$10, $11, $12,
		$13, $14, $15,
		$16, $17, $18
	)
`

func InsertSiteRecord(site *model.Site) (err error) {
	_, err = postgres.ExecByRW(
		insertSiteRecordSql,
		site.SiteId, site.UserId, site.SiteName,
		site.Domain, site.Repo, site.Branch,
		site.BuildImage, site.BuildCommand, site.PublishDirectory,
		site.BaseDirectory, site.LatestPublishedDeployId, site.CurrentFileHash,
		site.CurrentFileSize, site.LatestPublishedAt, site.IsDeleted,
		site.CreatedAt, site.UpdatedAt, site.RepoOwner,
	)
	return
}

const querySiteRecordSql = `select site_id, user_id, site_name,  
	domain, repo, branch,
	build_image, build_command, publish_directory, 
	base_directory, latest_published_deploy_id, current_file_hash, 
	current_file_size, latest_published_at, is_deleted, 
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
from 
	tb_site 
where 
	user_id = $1 and site_id = $2 
limit 1`

func QuerySiteRecord(userId, siteId string) (site *model.Site, err error) {
	site = &model.Site{}
	err = postgres.GetByRW(querySiteRecordSql, site, userId, siteId)
	return
}

const querySiteRecordBySiteNameSql = `select site_id, user_id, site_name,  
	domain, repo, branch,
	build_image, build_command, publish_directory, 
	base_directory, latest_published_deploy_id, current_file_hash, 
	current_file_size, latest_published_at, is_deleted, 
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
from 
	tb_site 
where 
	site_name = $1 and is_deleted = $2 
limit 1`

func QuerySiteRecordBySiteName(siteName string) (site *model.Site, err error) {
	site = &model.Site{}
	err = postgres.GetByRW(querySiteRecordBySiteNameSql, site, siteName, model.SiteDeletedNot)
	return
}

const querySiteRecordBySiteRepoSql = `select site_id, user_id, site_name,  
	domain, repo, branch,
	build_image, build_command, publish_directory, 
	base_directory, latest_published_deploy_id, current_file_hash, 
	current_file_size, latest_published_at, is_deleted, 
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
	repo_owner, is_auto_deploy, auto_deploy_secret
from 
	tb_site 
where 
	repo = $1 and repo_owner = $2 and is_auto_deploy = true and is_deleted = $3`

func QuerySiteDetailByRepo(repoArg *model.QuerySiteDetailByRepoArg) (sites []*model.Site, err error) {
	err = postgres.SelectByRW(querySiteRecordBySiteRepoSql, &sites, repoArg.Repo, repoArg.RepoOwner, model.SiteDeletedNot)
	return
}

const querySiteRecordsBySiteNamesSql = `select site_id, user_id, site_name,
	domain, repo, branch,
	build_image, build_command, publish_directory,
	base_directory, latest_published_deploy_id, current_file_hash,
	current_file_size, latest_published_at, is_deleted,
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
from
	tb_site
where
	site_name in %s and is_deleted = %d
`

func QuerySiteRecordsBySiteNames(siteNames []string) (sites []*model.Site, err error) {
	siteNamesStr := packSqlArrayString(siteNames)
	sql := fmt.Sprintf(querySiteRecordsBySiteNamesSql, siteNamesStr, model.SiteDeletedNot)
	fmt.Println("QuerySiteRecordsBySiteNames, sql = ", sql)

	err = postgres.SelectByRW(sql, &sites)
	return
}

func packSqlArrayString(s []string) string {
	str := ""
	for _, v := range s {
		str = str + "'" + v + "'" + ","
	}

	str = "(" + str[:len(str)-1] + ")"
	return str
}

const querySiteRecordsBySiteIdsSql = `select site_id, user_id, site_name,
	domain, repo, branch,
	build_image, build_command, publish_directory,
	base_directory, latest_published_deploy_id, current_file_hash,
	current_file_size, latest_published_at, is_deleted,
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
from
	tb_site
where
	site_id in %s and is_deleted = %d
`

func QuerySiteRecordsBySiteIds(siteIds []string) (sites []*model.Site, err error) {
	siteIdsStr := packSqlArrayString(siteIds)
	sql := fmt.Sprintf(querySiteRecordsBySiteIdsSql, siteIdsStr, model.SiteDeletedNot)
	fmt.Println("QuerySiteRecordsBySiteIds, sql = ", sql)

	err = postgres.SelectByRW(sql, &sites)
	return
}

const (
	querySiteListSql = `select site_id, user_id, site_name,  
	domain, repo, branch,
	build_image, build_command, publish_directory, 
	base_directory, latest_published_deploy_id, current_file_hash, 
	current_file_size, latest_published_at, is_deleted, 
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
from 
	tb_site 
where 
	(user_id = $1 or site_id in (%s)) and is_deleted = $2 
order by 
	id desc 
limit $3 offset $4`

	querySiteTotalSql = `select count(1) from tb_site where
	(user_id = $1 or site_id in (%s)) and is_deleted = $2`
)

func QuerySiteList(userId string, siteIdList []string, limit, offset int64) (list []*model.Site, total int64, err error) {
	if len(siteIdList) == 0 {
		siteIdList = append(siteIdList, `'000000'`)
	}
	err = postgres.SelectByRW(fmt.Sprintf(querySiteListSql, strings.Join(siteIdList, ",")), &list, userId, model.SiteDeletedNot, limit, offset)
	if err != nil {
		return
	}
	err = postgres.GetByRW(fmt.Sprintf(querySiteTotalSql, strings.Join(siteIdList, ",")), &total, userId, model.SiteDeletedNot)
	return
}

const getSiteLastStatListSql = `select sum(pv) as pv, sum(uv) as uv, sum(bandwidth) as bandwidth 
from 
    tb_site_visit_stat 
where 
	site_id = $1 
group by 
	stat_date 
order by 
	stat_date desc 
limit 1`

func GetSiteLastVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error) {
	batch := postgres.NewBatch()
	for _, siteId := range siteIdList {
		batch.Queue(getSiteLastStatListSql, siteId)
	}

	list = make([]*model.SiteVisitStatBasic, 0)
	err = batch.GetByRO(&list)
	if err != nil {
		return
	}

	if len(siteIdList) != len(list) {
		errMsg := fmt.Sprintf("GetSiteLastStatList batch.GetByRO len(siteIdList)[%d] != len(list)[%d]. ",
			len(siteIdList), len(list))
		err = errors.New(errMsg)
	}

	return
}

const getSite7daysStatListSql = `select COALESCE(sum(s.pv),0) as pv, COALESCE(sum(s.uv),0) as uv, COALESCE(sum(s.bandwidth),0) as bandwidth 
from (
	 select stat_date, sum(pv) as pv, sum(uv) as uv, sum(bandwidth) as bandwidth
	 from 
	     tb_site_visit_stat 
	 where 
	     site_id = $1
	 group by 
	     stat_date
	 order by 
	     stat_date desc
	 limit 7
     ) as s ;`

func GetSite7daysVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error) {
	batch := postgres.NewBatch()
	for _, siteId := range siteIdList {
		batch.Queue(getSite7daysStatListSql, siteId)
	}

	list = make([]*model.SiteVisitStatBasic, 0)
	err = batch.GetByRO(&list)
	if err != nil {
		return
	}

	if len(siteIdList) != len(list) {
		errMsg := fmt.Sprintf("GetSite7daysStatList batch.GetByRO len(siteIdList)[%d] != len(list)[%d]. ",
			len(siteIdList), len(list))
		err = errors.New(errMsg)
	}
	return
}

const getSiteLastNDaysStatListSql = `select stat_date, sum(pv) as pv, sum(uv) as uv, sum(bandwidth) as bandwidth 
from 
    tb_site_visit_stat 
where 
	site_id = $1 
group by 
	stat_date 
order by 
	stat_date desc 
limit $2`

func GetSiteLastNVisitDaysStatList(siteId string, days int) (list []*model.RespSiteDailyVisitStatList, err error) {
	err = postgres.SelectByRW(getSiteLastNDaysStatListSql, &list, siteId, days)
	if err != nil {
		return
	}

	return
}

const querySiteStatListSql = ` select site_id, user_id, site_name,  
	domain, repo, branch,
	build_image, build_command, publish_directory, 
	base_directory, latest_published_deploy_id, current_file_hash, 
	current_file_size, latest_published_at, is_deleted, 
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
from 
    tb_site 
where 
    1 = 1%s 
order by 
    created_at desc
`

func QuerySiteStatList(from, to *time.Time) (list []*model.Site, err error) {
	var args []interface{}
	var where string
	if from != nil {
		args = append(args, from)
		where += fmt.Sprintf(` and created_at >= $%d`, len(args))
	}
	if to != nil {
		args = append(args, to)
		where += fmt.Sprintf(` and created_at <= $%d`, len(args))
	}
	qs := fmt.Sprintf(querySiteStatListSql, where)
	err = postgres.SelectByRW(qs, &list, args...)
	return
}

const deleteSiteRecordSql = `update tb_site set 
	site_name = concat(site_name, '-DELETE-', id), 
	updated_at = (now() at time zone 'utc'), 
	is_deleted = $1
where 
	site_id = $2`

func DeleteSiteRecord(siteId string) (deleted bool, err error) {
	tg, err := postgres.ExecByRW(deleteSiteRecordSql, model.SiteDeleteYes, siteId)
	deleted = tg.RowsAffected() > 0
	return
}

const deleteSiteRecordWithSiteIdSql = `update tb_site set 
	site_name = concat(site_name, '-DELETE-', id), 
	site_id = concat(site_id, '-DELETE-', id),
	updated_at = (now() at time zone 'utc'), 
	is_deleted = $1
where 
	site_id = $2`

func DeleteSiteRecordWithSiteId(siteId string) (deleted bool, err error) {
	tg, err := postgres.ExecByRW(deleteSiteRecordWithSiteIdSql, model.SiteDeleteYes, siteId)
	deleted = tg.RowsAffected() > 0
	return
}

const updateSiteNameSql = `update tb_site set 
	site_name = $1, domain = $2, updated_at = (now() at time zone 'utc')
where
	site_id = $3 and is_deleted = $4 and not exists (
		select 1 from tb_site where site_name = $5
	)`

func UpdateSiteName(siteId, siteName, domain string) (updated bool, err error) {
	tg, err := postgres.ExecByRW(
		updateSiteNameSql, siteName, domain,
		siteId, model.SiteDeletedNot, siteName,
	)
	updated = tg.RowsAffected() > 0
	return
}

const updateSiteDeploySignatureSql = `update tb_deploy set 
   status = $1, signature = $2, signature_message = $3, updated_at = (now() at time zone 'utc')
where 
   user_id= $4 and deploy_id = $5 and status = $6`

const updateSiteSignatureSql = `update tb_site set 
	current_signature = $1, current_signature_message = $2, once_signed = true, updated_at = (now() at time zone 'utc')
where
	user_id = $3 and latest_published_deploy_id = $4 and is_deleted = $5`

func UpdateDeploySignature(userId, deployId, signature, signatureMessage string) (err error) {
	err = postgres.BeginFuncByRW(func(tx postgres.Tx) (txErr error) {
		cmd, txErr := tx.Exec(updateSiteDeploySignatureSql, model.DeployStatusConfirmedStatus, signature, signatureMessage,
			userId, deployId, model.DeployStatusPublished)
		if txErr != nil {
			return
		}

		if cmd.RowsAffected() == 0 {
			return errors.New("not found")
		}

		cmd, txErr = tx.Exec(updateSiteSignatureSql, signature, signatureMessage,
			userId, deployId, model.SiteDeletedNot)
		if txErr != nil {
			return
		}

		if cmd.RowsAffected() == 0 {
			return errors.New("not found")
		}

		return
	})
	return
}

type SiteSettingUpdateFields struct {
	Repo             string
	Branch           string
	BuildImage       string
	BuildCommand     string
	PublishDirectory string
	BaseDirectory    string
}

const updateSiteSettingSql = `update tb_site set
	repo = $1, branch = $2, build_image = $3,
	build_command = $4, publish_directory = $5, base_directory = $6,
	updated_at = (now() at time zone 'utc')
where
	site_id = $7
 `

func UpdateSiteSetting(siteId string, fields *SiteSettingUpdateFields) (updated bool, err error) {
	tg, err := postgres.ExecByRW(
		updateSiteSettingSql, fields.Repo, fields.Branch, fields.BuildImage,
		fields.BuildCommand, fields.PublishDirectory, fields.BaseDirectory,
		siteId,
	)
	updated = tg.RowsAffected() > 0
	return
}

type SitePublishUpdateFields struct {
	LatestPublishedDeployId string
	CurrentFileHash         string
	CurrentFileSize         int64
	LatestPublishedAt       time.Time
	DeploymentDuration      float64
}

const (
	updateSiteDeployOutdatedSql = `update tb_deploy set 
	status = $1, updated_at = (now() at time zone 'utc') 
where 
	site_id = $2 and status = $3`

	updateSiteDeployPublishedSql = `update tb_deploy set 
	status = $1, finished_at = $2, updated_at = (now() at time zone 'utc')
where 
	deploy_id = $3`

	updateSitePublishSql = `update tb_site set
	latest_published_deploy_id = $1, current_file_hash = $2, current_file_size = $3, latest_published_at = $4, 
	current_signature = '', current_signature_message = '', deployment_duration = $5, 
	updated_at = (now() at time zone 'utc')
where 
	site_id = $6`
)

func UpdateSitePublishStatus(siteId string, fields *SitePublishUpdateFields) (err error) {
	err = postgres.BeginFuncByRW(func(tx postgres.Tx) (txErr error) {
		_, txErr = tx.Exec(
			updateSiteDeployOutdatedSql, model.DeployStatusOutdated,
			siteId, model.DeployStatusPublished,
		)
		if txErr != nil {
			return
		}
		_, txErr = tx.Exec(
			updateSiteDeployPublishedSql, model.DeployStatusPublished, fields.LatestPublishedAt,
			fields.LatestPublishedDeployId,
		)
		if txErr != nil {
			return
		}
		_, txErr = tx.Exec(
			updateSitePublishSql, fields.LatestPublishedDeployId, fields.CurrentFileHash,
			fields.CurrentFileSize, fields.LatestPublishedAt, fields.DeploymentDuration, siteId,
		)
		return
	})
	return
}

const queryAllUsersSiteTotalSql = `select count(1) from tb_site where
	is_deleted = $1`

func GetAllUsersSiteTotal() (total int64, err error) {
	err = postgres.GetByRW(queryAllUsersSiteTotalSql, &total, model.SiteDeletedNot)
	return
}

const queryAllUsersSiteSizeTotalSql = `select sum(current_file_size) from tb_site where
	is_deleted = $1`

func GetAllUsersSiteSizeTotal() (total int64, err error) {
	err = postgres.GetByRW(queryAllUsersSiteSizeTotalSql, &total, model.SiteDeletedNot)
	return
}

const querySiteSizeUserTotalSql = `select count(distinct user_id) from tb_site where
	is_deleted = $1 and current_file_size > 0`

func GetSiteSizeUserTotalSql() (total int64, err error) {
	err = postgres.GetByRW(querySiteSizeUserTotalSql, &total, model.SiteDeletedNot)
	return
}

const queryGetUserSiteStatsSql = `select  coalesce(count(1), 0) as user_total_site, coalesce(sum(current_file_size), 0) as user_total_file_size 
from tb_site where user_id = $1 and is_deleted = $2`

func GetUserSiteStats(userId string) (stats *model.UserSiteStats, err error) {
	stats = &model.UserSiteStats{}
	err = postgres.GetByRW(queryGetUserSiteStatsSql, stats, userId, model.SiteDeletedNot)
	return
}

const updateSiteAutoDeploysSql = `update tb_site set 
	is_auto_deploy = $1, auto_deploy_secret = $2, updated_at = (now() at time zone 'utc')
where
	site_id = $3 and user_id = $4 and is_deleted = $5 `

func UpdateSiteAutoDeploy(userId, siteId, secret string, isAutoDeploy bool) (updated bool, err error) {
	tg, err := postgres.ExecByRW(
		updateSiteAutoDeploysSql, isAutoDeploy, secret, siteId, userId,
		model.SiteDeletedNot,
	)
	updated = tg.RowsAffected() > 0
	return
}

const regenerateSiteAutoDeploySecretSql = `update tb_site set 
	auto_deploy_secret = $1, updated_at = (now() at time zone 'utc')
where
	site_id = $2 and user_id = $3 and is_deleted = $4 `

func RegenerateSiteAutoDeploySecret(userId, siteId, secret string) (updated bool, err error) {
	tg, err := postgres.ExecByRW(
		regenerateSiteAutoDeploySecretSql, secret, siteId, userId,
		model.SiteDeletedNot,
	)
	updated = tg.RowsAffected() > 0
	return
}

const deleteTransferSiteRecordSql = `update tb_site set 
	is_deleted = $1, updated_at = (now() at time zone 'utc')
where
	site_id = $2 and user_id=$3 and is_deleted=0`

func DeleteTransferSiteRecord(siteId string, userId string) (err error) {
	tg, err := postgres.ExecByRW(deleteTransferSiteRecordSql, model.SiteDeleteYes, siteId, userId)
	if tg.RowsAffected() == 0 {
		return errors.New("site doesn't exists")
	}

	return
}

const unDeleteTransferSiteRecordSql = `update tb_site set 
	is_deleted = $1, updated_at = (now() at time zone 'utc')
where
	site_id = $2 and user_id=$3 and is_deleted=1`

func UnDeleteTransferSiteRecord(siteId string, userId string) error {
	affected, err := postgres.ExecByRW(unDeleteTransferSiteRecordSql, model.SiteDeletedNot, siteId, userId)
	if err != nil {
		return err
	}
	if affected.RowsAffected() == 0 {
		return errors.New("site doesn't exists")
	}
	return err
}

const getTransferSiteRecordSql = `
 	select site_id, user_id, site_name,  
	domain, repo, branch,
	build_image, build_command, publish_directory, 
	base_directory, latest_published_deploy_id, current_file_hash, 
	current_file_size, latest_published_at, is_deleted, 
	created_at, updated_at, current_signature, current_signature_message, once_signed, deployment_duration, 
    repo_owner, is_auto_deploy, auto_deploy_secret
	from tb_site where site_id = $1 and user_id = $2`

func GetTransferSiteRecord(siteId string, userId string) (site *model.Site, err error) {
	site = &model.Site{}
	err = postgres.GetByRW(getTransferSiteRecordSql, site, siteId, userId)
	return
}

const updateSiteGithubInfoSql = `update tb_site set
	repo = $1, branch = $2, build_image=$3,
	build_command=$4, publish_directory=$5, base_directory = $6,latest_published_deploy_id = $7, current_file_hash = $8,
    current_file_size = $9, latest_published_at = $10, repo_owner = $11,updated_at = (now() at time zone 'utc')
where
	site_id = $12 and user_id = $13 and is_deleted = $14 `

func UpdateSiteGithubInfo(site *model.Site) error {
	_, err := postgres.ExecByRW(updateSiteGithubInfoSql, site.Repo, site.Branch, site.BuildImage,
		site.BuildCommand, site.PublishDirectory, site.BaseDirectory, site.LatestPublishedDeployId, site.CurrentFileHash,
		site.CurrentFileSize, site.LatestPublishedAt, site.RepoOwner, site.SiteId, site.UserId, model.SiteDeletedNot)
	return err
}
