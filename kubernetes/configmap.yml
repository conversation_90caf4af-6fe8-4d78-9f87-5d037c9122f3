apiVersion: v1
kind: ConfigMap
metadata:
  name: $SERVICE_NAME-configmap
  namespace: $NAMESPACE
data:
  GIT_COMMIT_SHA: $CI_COMMIT_SHA
  GO111MODULE: "on"
  
  LOG_LEVEL: "$LOG_LEVEL"

  DB_RW_USER: "$DB_RW_USER"
  DB_RW_PASSWORD: "$DB_RW_PASSWORD"
  DB_RW_HOST: "$DB_RW_HOST"
  DB_RW_PORT: "$DB_RW_PORT"
  DB_RW_NAME: "$DB_RW_NAME"
  DB_RW_MAX_OPEN_CONNS: "$DB_RW_MAX_OPEN_CONNS"
  DB_RW_MAX_CONN_LIFE_TIME: "$DB_RW_MAX_CONN_LIFE_TIME"
  DB_RW_MAX_CONN_IDLE_TIME: "$DB_RW_MAX_CONN_IDLE_TIME"

  DB_RO_USER: "$DB_RO_USER"
  DB_RO_PASSWORD: "$DB_RO_PASSWORD"
  DB_RO_HOST: "$DB_RO_HOST"
  DB_RO_PORT: "$DB_RO_PORT"
  DB_RO_NAME: "$DB_RO_NAME"
  DB_RO_MAX_OPEN_CONNS: "$DB_RO_MAX_OPEN_CONNS"
  DB_RO_MAX_CONN_LIFE_TIME: "$DB_RO_MAX_CONN_LIFE_TIME"
  DB_RO_MAX_CONN_IDLE_TIME: "$DB_RO_MAX_CONN_IDLE_TIME"
  LOG_DB_NAME: "$LOG_DB_NAME"

  REDIS_HOST: "$REDIS_HOST"
  REDIS_PASSWORD: "$REDIS_PASSWORD"
  REDIS_NETWORK: "$REDIS_NETWORK"
  REDIS_DB: "$REDIS_DB"
  REDIS_MAX_IDLE_CONN: "$REDIS_MAX_IDLE_CONN"
  REDIS_MAX_ACTIVE_CONN: "$REDIS_MAX_ACTIVE_CONN"
  REDIS_CONN_IDLE_TIMEOUT: "$REDIS_CONN_IDLE_TIMEOUT"
  REDIS_CONN_WAIT: "$REDIS_CONN_WAIT"
  REDIS_MAX_LIFE_TIME: "$REDIS_MAX_LIFE_TIME"

  API_ADDR: "0.0.0.0:$SERVICE_PORT"

  BTFS_HOST: "$BTFS_HOST"
  BTFS_UPLOAD_API_PATH: "$BTFS_UPLOAD_API_PATH"
  HOST_IPS: "$HOST_IPS"
  S3_HOST: "$S3_HOST"
  S3_HOST_IPS: "$S3_HOST_IPS"
  VISIT_TYPE: "$VISIT_TYPE"

  DNS_ROOT_DOMAIN: "$DNS_ROOT_DOMAIN"
  DNS_ZONE_ID: "$DNS_ZONE_ID"

  GITHUB_APP_ID: "$GITHUB_APP_ID"
  GITHUB_CLIENT_ID: "$GITHUB_CLIENT_ID"
  GITHUB_CLIENT_SECRET: "$GITHUB_CLIENT_SECRET"
  GITHUB_PRIVATE_KEY_PATH: "$GITHUB_PRIVATE_KEY_PATH"
  GITHUB_ENDPOINT: "$GITHUB_ENDPOINT"

  KAFKA_BOOTSTRAP_SERVERS: "$KAFKA_BOOTSTRAP_SERVERS"

  DEPLOY_JOB_TOPIC: "$DEPLOY_JOB_TOPIC"
  DEPLOY_JOB_GROUP_ID: "$DEPLOY_JOB_GROUP_ID"
  DEPLOY_JOB_OFFSET_RESET: "$DEPLOY_JOB_OFFSET_RESET"
  DEPLOY_JOB_ROUTINES: "$DEPLOY_JOB_ROUTINES"

  DOCKER_HOST: "$DOCKER_HOST"

  SITE_NAME_RESERVED_WORDS: "$SITE_NAME_RESERVED_WORDS"
  SITE_NAME_RESERVED_WORDS_ALLOWED_USER_ADDRESSES: "$SITE_NAME_RESERVED_WORDS_ALLOWED_USER_ADDRESSES"
  USER_ADDRESSES_WHITELIST: "$USER_ADDRESSES_WHITELIST"

  TRON_API_HOST: "$TRON_API_HOST"
  TRON_API_KEY: "$TRON_API_KEY"
  PROFITS_SUPER_USER_ADDRESSES: "$PROFITS_SUPER_USER_ADDRESSES"
  PROFITS_FREE_STORE_SIZE: "$PROFITS_FREE_STORE_SIZE"
  PROFITS_GOLD_NFT_HOLDER_STORE_SIZE: "$PROFITS_GOLD_NFT_HOLDER_STORE_SIZE"
  PROFITS_SILVER_NFT_HOLDER_STORE_SIZE: "$PROFITS_SILVER_NFT_HOLDER_STORE_SIZE"
  PROFITS_BRONZE_NFT_HOLDER_STORE_SIZE: "$PROFITS_BRONZE_NFT_HOLDER_STORE_SIZE"
  PROFITS_FREE_SITE_SIZE: "$PROFITS_FREE_SITE_SIZE"
  PROFITS_GOLD_HOLDER_SITE_SIZE: "$PROFITS_GOLD_HOLDER_SITE_SIZE"
  PROFITS_SILVER_NFT_HOLDER_SITE_SIZE: "$PROFITS_SILVER_NFT_HOLDER_SITE_SIZE"
  PROFITS_BRONZE_NFT_HOLDER_SITE_SIZE: "$PROFITS_BRONZE_NFT_HOLDER_SITE_SIZE"
  PROFITS_FREE_SITE_NUM: "$PROFITS_FREE_SITE_NUM"
  PROFITS_GOLD_NFT_HOLDER_SITE_NUM: "$PROFITS_GOLD_NFT_HOLDER_SITE_NUM"
  PROFITS_SILVER_NFT_HOLDER_SITE_NUM: "$PROFITS_SILVER_NFT_HOLDER_SITE_NUM"
  PROFITS_BRONZE_NFT_HOLDER_SITE_NUM: "$PROFITS_BRONZE_NFT_HOLDER_SITE_NUM"

  AWS_OUTPUT_BUCKET: "$AWS_OUTPUT_BUCKET"
  AWS_REGION: "$AWS_REGION"

  AWS_S3_BUCKET: "$AWS_S3_BUCKET"
  AWS_S3_ACCESS_ID: "$AWS_S3_ACCESS_ID"
  AWS_S3_SECRET_ACCESS_KEY: "$AWS_S3_SECRET_ACCESS_KEY"
  AWS_S3_REGION: "$AWS_S3_REGION"