package data

import (
	"context"
	"errors"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

const insertKey = `insert into tb_key
	(user_id, access_key, secret, location) values 
	($1, $2, $3, $4)`

const updateKeyActiveState = `UPDATE tb_key SET "is_enabled" = $1 WHERE "access_key" = $2;`

const updateKeySecret = `UPDATE tb_key SET "secret" = $1 WHERE "access_key" = $2;`

const deleteKey = `UPDATE tb_key SET "is_deleted" = true WHERE "access_key" = $1;`

const detailKey = `select id, user_id, access_key, secret,is_enabled, is_deleted, created_at, updated_at, location from tb_key where "access_key"=$1 and is_deleted = false`

const queryKeyList = `select id, user_id, access_key, secret, is_enabled, is_deleted, created_at, updated_at 
from 
	tb_key
where 
	user_id = $1 and is_deleted = false
order by 
	id desc`

func CreateKey(ctx context.Context, key *model.Key) error {
	_, err := postgres.ExecByRW(insertKey,
		key.UserId, key.AccessKey, key.Secret, key.Location)
	return err
}

func UpdateActiveState(ctx context.Context, key string, state bool) error {
	_, err := postgres.ExecByRW(updateKeyActiveState, state, key)
	return err
}

func DeleteKey(key string) error {
	_, err := postgres.ExecByRW(deleteKey, key)
	return err
}

func DetailKey(ctx context.Context, key string) (*model.Key, error) {
	k := &model.Key{}
	err := postgres.GetByRO(detailKey, k, key)
	if errors.Is(err, postgres.ErrNoRows) {
		return nil, nil
	}
	return k, err
}

func ListKey(ctx context.Context, userId string) ([]*model.Key, error) {
	list := make([]*model.Key, 0)
	err := postgres.SelectByRO(queryKeyList, &list, userId)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func ResetKey(key string, secret string) error {
	_, err := postgres.ExecByRW(updateKeySecret, secret, key)
	return err
}
