package model

import (
	"regexp"
	"time"
)

const (
	_                              = iota
	TransferStatusInitiatorPending // 发起者 pending
	TransferStatuReceivePending    // 接收者pending
	TransferStatusReceived         // 已接收, 在重新部署的时候是使用之前的站点信息
)

type Site struct {
	SiteId                  string            `json:"site_id"`
	UserId                  string            `json:"user_id"`
	SiteName                string            `json:"site_name"`
	Domain                  string            `json:"domain"`
	RepoOwner               string            `json:"repo_owner"`
	Repo                    string            `json:"repo"`
	Branch                  string            `json:"branch"`
	BuildImage              string            `json:"build_image"`
	BuildCommand            string            `json:"build_command"`
	PublishDirectory        string            `json:"publish_directory"`
	BaseDirectory           string            `json:"base_directory"`
	LatestPublishedDeployId string            `json:"latest_published_deploy_id"`
	CurrentFileHash         string            `json:"current_file_hash"`
	CurrentFileSize         int64             `json:"current_file_size"`
	LatestPublishedAt       time.Time         `json:"latest_published_at"`
	IsDeleted               SiteDeletedStatus `json:"is_deleted"`
	CreatedAt               time.Time         `json:"created_at"`
	UpdatedAt               time.Time         `json:"updated_at"`
	CurrentSignature        string            `json:"current_signature"`
	CurrentSignatureMessage string            `json:"current_signature_message"`
	OnceSigned              bool              `json:"once_signed"`
	DeploymentDuration      float64           `json:"deployment_duration"`
	IsAutoDeploy            bool              `json:"is_auto_deploy"`
	AutoDeploySecret        string            `json:"auto_deploy_secret"`
	TransferStatus          int               `json:"transfer_status"`
	TransferCreatedAt       time.Time         `json:"transfer_created_at"`
	TransferFrom            string            `json:"transfer_from"`
	TransferFromAddress     string            `json:"transfer_from_address"`
}

type SiteVisitStatBasic struct {
	Pv        int64 `json:"pv"`
	Uv        int64 `json:"uv"`
	Bandwidth int64 `json:"bandwidth"`
}

type SiteStatMore struct {
	Site
	LastDailyStat SiteVisitStatBasic `json:"last_daily_stat"`
}

type SiteDeletedStatus int

const (
	SiteDeletedNot SiteDeletedStatus = iota
	SiteDeleteYes
)

type CreateSiteArg struct {
	SiteId           string `json:"site_id"`
	SiteName         string `json:"site_name"`
	RepoOwner        string `json:"repo_owner" binding:"required,lte=512"`
	Repo             string `json:"repo" binding:"required,lte=512"`
	Branch           string `json:"branch" binding:"required,gte=1,lte=128"`
	BuildImage       string `json:"build_image" binding:"lte=128"`
	BuildCommand     string `json:"build_command" binding:"lte=1024"`
	PublishDirectory string `json:"publish_directory" binding:"abspath,lte=128"`
	BaseDirectory    string `json:"base_directory" binding:"lte=128"`
}

type QuerySiteListArg struct {
	Limit  int64 `json:"limit" binding:"gte=1,lte=100"`
	Offset int64 `json:"offset" binding:"gte=0"`
}

type QuerySiteDetailArg struct {
	SiteId string `json:"site_id" binding:"gte=1,lte=128"`
}

type DeleteSiteArg struct {
	SiteId string `json:"site_id" binding:"gte=1,lte=128"`
}

type UpdateSiteNameArg struct {
	SiteId   string `json:"site_id" binding:"gte=1,lte=128"`
	SiteName string `json:"site_name" binding:"required,sitename,gte=3,lte=32"`
}

type UpdateDeploySignatureArg struct {
	DeployId         string `json:"deploy_id" binding:"gte=1,lte=128"`
	Signature        string `json:"signature" binding:"required,gte=1,lte=512"`
	SignatureMessage string `json:"signature_message" binding:"required,gte=1,lte=2048"`
}

type UpdateSiteAutoDeployArg struct {
	SiteId       string `json:"site_id" binding:"required,gte=1,lte=128"`
	IsAutoDeploy *bool  `json:"is_auto_deploy" binding:"required"`
}

type RegenerateSiteAutoDeploySecretArg struct {
	SiteId string `json:"site_id" binding:"required,gte=1,lte=128"`
}

type UpdateSiteSettingArg struct {
	SiteId           string `json:"site_id" binding:"required,gte=1,lte=128"`
	Repo             string `json:"repo" binding:"required,gte=1,lte=512"`
	Branch           string `json:"branch" binding:"required,gte=1,lte=128"`
	BuildImage       string `json:"build_image" binding:"lte=128"`
	BuildCommand     string `json:"build_command" binding:"lte=1024"`
	PublishDirectory string `json:"publish_directory" binding:"abspath,lte=128"`
	BaseDirectory    string `json:"base_directory" binding:"lte=128"`
}

var siteNameRegexp = regexp.MustCompile(`^[a-z0-9]+[a-z0-9-]*[a-z0-9]+$`)

func IsValidSiteName(name string) bool {
	return siteNameRegexp.MatchString(name)
}

type SiteMore struct {
	Site
	Address        string     `json:"address"`
	Source         UserSource `json:"source"`
	GithubLogin    string     `json:"github_login"`
	GithubRepoPath string     `json:"github_repo_path"`
	CommitId       string     `json:"commit_id"`
}

type QuerySiteDetailBySiteNameArg struct {
	SiteName string `json:"site_name" binding:"required,sitename,gte=3,lte=32"`
}

type QuerySiteDetailByRepoArg struct {
	RepoOwner string `json:"repo_owner" binding:"required,lte=512"`
	Repo      string `json:"repo" binding:"required,lte=512"`
}

type BatchGetSiteInfoArg struct {
	SiteIdList []string `json:"site_id_list" binding:"required,gte=1,lte=50"`
}

type GetSiteSignatureStatListRetOne struct {
	SiteId                  string    `json:"site_id"`
	SiteName                string    `json:"site_name"`
	Domain                  string    `json:"domain"`
	CurrentSignature        string    `json:"current_signature"`
	CurrentSignatureMessage string    `json:"current_signature_message"`
	OnceSigned              bool      `json:"once_signed"`
	LastDeploymentTime      time.Time `json:"last_deployment_time"`
	PvSortWeight            int64     `json:"pv_sort_weight"`
	UvSortWeight            int64     `json:"uv_sort_weight"`
}

type QuerySiteStatListArg struct {
	From *time.Time `json:"from" form:"from"`
	To   *time.Time `json:"to" form:"to"`
}

type GetSiteDailyVisitStatListArg struct {
	SiteId string `json:"site_id" binding:"required,gte=1,lte=64"`
	Days   int    `json:"days" binding:"required,gte=1,lte=365"`
}
type RespSiteDailyVisitStatList struct {
	PV        int64     `json:"pv"`
	UV        int64     `json:"uv"`
	Bandwidth int64     `json:"bandwidth"`
	StatDate  time.Time `json:"stat_date"`
}

//  site transfer

const (
	TransferStatusAcceptedAndCreated = iota
	TransferStatusPending
	TransferStatusAccepted
	TransferStatusDeclined
	TransferStatusExpired
)

type SiteTransferRecord struct {
	Id                   int64      `json:"id"`
	SiteId               string     `json:"site_id"`
	FromAddress          string     `json:"from_address"`
	FromUserId           string     `json:"from_user_id"`
	FromAddressType      UserSource `json:"from_address_type"`
	FromSignature        string     `json:"from_signature"`
	FromSignatureMessage string     `json:"from_signature_message"`
	ToAddress            string     `json:"to_address"`
	ToAddressType        UserSource `json:"to_address_type"`
	ToUserId             string     `json:"to_user_id"`
	ToSignature          string     `json:"to_signature"`
	ToSignatureMessage   string     `json:"to_signature_message"`
	TransferStatus       int        `json:"transfer_status"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

type TransferSiteArg struct {
	SiteId            string    `json:"site_id" binding:"required,gte=1,lte=128"`
	Signature         string    `json:"signature" binding:"required,gte=1,lte=512"`
	SignatureMessage  string    `json:"signature_message" binding:"required,gte=1,lte=2048"`
	TargetAddress     string    `json:"target_address" binding:"required,gte=1,lte=128"`
	TargetAddressType string    `json:"target_address_type" form:"source" binding:"required,oneof=BTTC TRON"`
	TransferCreatedAt time.Time `json:"transfer_created_at" binding:"required"`
}

type TransferSiteOpArg struct {
	SiteId           string `json:"site_id" binding:"required,gte=1,lte=128"`
	Operation        string `json:"operation" binding:"required,oneof=ACCEPT DECLINE"`
	Signature        string `json:"signature" binding:"gte=0,lte=512"`
	SignatureMessage string `json:"signature_message" binding:"gte=0,lte=2048"`
}

type SiteHistoryArg struct {
	SiteId string `json:"site_id"`
}

type CheckTargetAddressArg struct {
	TargetAddress     string `json:"target_address" binding:"required,gte=1,lte=128"`
	TargetAddressType string `json:"target_address_type" form:"source" binding:"required,oneof=BTTC TRON"`
}
