package log

import "github.com/sirupsen/logrus"

func IfErr(err error) {
	if err != nil {
		<PERSON>rro<PERSON>(err)
	} else {
		Info("Ok")
	}
}

func WithFields(fields Fields) *Entry {
	return &Entry{
		oentry: logger.WithFields(logrus.Fields(fields)),
	}
}

func WithField(key string, val interface{}) *Entry {
	return &Entry{
		oentry: logger.WithField(key, val),
	}
}

func Info(args ...interface{}) {
	logger.Info(args...)
}

func Infof(format string, args ...interface{}) {
	logger.Infof(format, args...)
}

func Error(args ...interface{}) {
	logger.Error(args...)
}

func Errorf(format string, args ...interface{}) {
	logger.Errorf(format, args...)
}

func Debug(args ...interface{}) {
	logger.Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	logger.Debugf(format, args...)
}

func Warn(args ...interface{}) {
	logger.Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	logger.Warnf(format, args...)
}

func Panic(args ...interface{}) {
	logger.Panic(args...)
}

func Panicf(format string, args ...interface{}) {
	logger.Panicf(format, args...)
}
