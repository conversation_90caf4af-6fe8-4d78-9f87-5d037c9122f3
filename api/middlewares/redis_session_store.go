package middlewares

import (
	"context"
	"encoding/base32"
	"encoding/json"
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gorilla/securecookie"
	gorillaSessions "github.com/gorilla/sessions"
	"github.com/redis/go-redis/v9"
)

// RedisSessionStore implements both gorilla/sessions.Store and gin-contrib/sessions.Store
type RedisSessionStore struct {
	client    redis.UniversalClient
	codecs    []securecookie.Codec
	options   *gorillaSessions.Options
	keyPrefix string
}

// NewRedisSessionStore creates a new Redis session store
func NewRedisSessionStore(client redis.UniversalClient, keyPairs ...[]byte) *RedisSessionStore {
	return &RedisSessionStore{
		client:    client,
		codecs:    securecookie.CodecsFromPairs(keyPairs...),
		options:   &gorillaSessions.Options{Path: "/", MaxAge: 86400 * 30, HttpOnly: true},
		keyPrefix: "session:",
	}
}

// Get returns a session for the given name after adding it to the registry
func (s *RedisSessionStore) Get(r *http.Request, name string) (*gorillaSessions.Session, error) {
	return gorillaSessions.GetRegistry(r).Get(s, name)
}

// New returns a session for the given name without adding it to the registry
func (s *RedisSessionStore) New(r *http.Request, name string) (*gorillaSessions.Session, error) {
	session := gorillaSessions.NewSession(s, name)
	opts := *s.options
	session.Options = &opts
	session.IsNew = true

	c, err := r.Cookie(name)
	if err != nil {
		return session, nil
	}

	err = securecookie.DecodeMulti(name, c.Value, &session.ID, s.codecs...)
	if err != nil {
		return session, nil
	}

	err = s.load(session)
	if err == nil {
		session.IsNew = false
	}

	return session, nil
}

// Save adds a single session to the response
func (s *RedisSessionStore) Save(r *http.Request, w http.ResponseWriter, session *gorillaSessions.Session) error {
	if session.Options.MaxAge <= 0 {
		if err := s.delete(session); err != nil {
			return err
		}
		http.SetCookie(w, gorillaSessions.NewCookie(session.Name(), "", session.Options))
		return nil
	}

	if session.ID == "" {
		session.ID = strings.TrimRight(base32.StdEncoding.EncodeToString(securecookie.GenerateRandomKey(32)), "=")
	}

	if err := s.save(session); err != nil {
		return err
	}

	encoded, err := securecookie.EncodeMulti(session.Name(), session.ID, s.codecs...)
	if err != nil {
		return err
	}

	http.SetCookie(w, gorillaSessions.NewCookie(session.Name(), encoded, session.Options))
	return nil
}

// Options sets the default options for each session stored in this RedisSessionStore
// This method implements gin-contrib/sessions.Store interface
func (s *RedisSessionStore) Options(options sessions.Options) {
	s.options = &gorillaSessions.Options{
		Path:     options.Path,
		Domain:   options.Domain,
		MaxAge:   options.MaxAge,
		Secure:   options.Secure,
		HttpOnly: options.HttpOnly,
		SameSite: options.SameSite,
	}
}

// load reads the session from Redis
func (s *RedisSessionStore) load(session *gorillaSessions.Session) error {
	ctx := context.Background()
	data, err := s.client.Get(ctx, s.keyPrefix+session.ID).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return errors.New("session not found")
		}
		return err
	}

	return json.Unmarshal([]byte(data), &session.Values)
}

// save writes the session to Redis
func (s *RedisSessionStore) save(session *gorillaSessions.Session) error {
	ctx := context.Background()
	data, err := json.Marshal(session.Values)
	if err != nil {
		return err
	}

	maxAge := session.Options.MaxAge
	if maxAge == 0 {
		maxAge = 86400 * 30 // 30 days default
	}

	return s.client.Set(ctx, s.keyPrefix+session.ID, data, time.Duration(maxAge)*time.Second).Err()
}

// delete removes the session from Redis
func (s *RedisSessionStore) delete(session *gorillaSessions.Session) error {
	ctx := context.Background()
	return s.client.Del(ctx, s.keyPrefix+session.ID).Err()
}
