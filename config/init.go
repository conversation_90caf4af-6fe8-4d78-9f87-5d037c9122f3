package config

import (
	"gitlab.insidebt.net/btfs/storage3-backend/env"
)

var (
	Log       = new(LogSt)
	DBRW      = new(DBRWSt)
	DBRO      = new(DBROSt)
	Redis     = new(RedisSt)
	API       = new(APISt)
	BTFS      = new(BTFSSt)
	DNS       = new(DNSSt)
	GitHub    = new(GitHubAppSt)
	Kafka     = new(KafkaSt)
	DeployJob = new(DeployJobSt)
	SiteName  = new(SiteNameSt)
	Tron      = new(TronSt)
	Profits   = new(ProfitsSt)
	Aws       = new(AwsSt)
	AwsS3     = new(AwsS3St)
)

func init() {
	env.ScanTo(Log)
	env.ScanTo(DBRW)
	env.ScanTo(DBRO)
	env.ScanTo(Redis)
	env.ScanTo(API)
	env.ScanTo(BTFS)
	env.ScanTo(DNS)
	env.ScanTo(GitHub)
	env.ScanTo(Kafka)
	env.ScanTo(DeployJob)
	env.ScanTo(SiteName)
	env.ScanTo(Tron)
	env.ScanTo(Profits)
	env.ScanTo(Aws)
	env.ScanTo(AwsS3)
}
