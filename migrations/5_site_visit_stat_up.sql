create table if not exists tb_site_visit_stat
(
    id         bigserial primary key,
    site_id    character varying(128)      not null default ''::character varying,
    domain     character varying(128)      not null default ''::character varying,
    pv         bigint                      not null default '0'::bigint,
    uv         bigint                      not null default '0'::bigint,
    bandwidth  bigint                      not null default '0'::bigint,
    stat_date  timestamp without time zone not null default (now() at time zone 'utc'),
    from_time  timestamp without time zone not null default (now() at time zone 'utc'),
    to_time    timestamp without time zone not null default (now() at time zone 'utc'),
    created_at timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at timestamp without time zone not null default (now() at time zone 'utc'),
    unique (site_id, stat_date, from_time)
);
