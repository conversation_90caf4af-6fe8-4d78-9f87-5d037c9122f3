package data

import (
	"errors"
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
	"strings"
)

const insertObject = `insert into tb_object
	(bucket_name, access_key, content_name, size, cid, content_type, content_encoding, expires, etag, location) values 
	($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`

const getObject = `select bucket_name, access_key, content_name, location, size, cid, content_type, content_encoding, expires, etag, created_at, updated_at from tb_object where access_key =$1 and bucket_name=$2 and content_name = $3 and is_deleted=false`
const getObjectByCid = `select bucket_name, access_key, content_name, location, size, cid, content_type, content_encoding, expires, etag, created_at, updated_at from tb_object where cid = $1 and is_deleted=false`

const updateFolderDelete = `update tb_object set is_deleted=true where access_key=$1 and bucket_name=$2 and content_name=$3 and is_deleted=false`

const updateObject = `update tb_object set size=$1, cid=$2, content_type=$3, content_encoding=$4, expires=$5, etag=$6 where access_key=$7 and bucket_name=$8 and content_name=$9 and is_deleted=false`

const queryFolderList = `select access_key, bucket_name, content_name, content_type, cid, location,size, content_encoding, expires, etag, created_at, updated_at 
				from tb_object
				where access_key=$1 and bucket_name=$2 and is_deleted=false and content_name like $3`

const deleteObjects = `update tb_object set is_deleted=true where %s`

func InsertObject(object *model.Object) error {
	_, err := postgres.ExecByRW(insertObject,
		object.BucketName, object.AccessKey, object.ContentName, object.Size, object.Cid, object.ContentType, object.ContentEncoding, object.Expires, object.Etag, object.Location)
	return err
}

func UpsertObject(object *model.Object) error {
	o, err := GetObject(object.AccessKey, object.BucketName, object.ContentName)
	if err != nil {
		return err
	}

	if o != nil {
		_, err = postgres.ExecByRW(updateObject, object.Size, object.Cid, object.ContentType, object.ContentEncoding, object.Expires, object.Etag, object.BucketName, object.AccessKey, object.ContentName)
	} else {
		_, err = postgres.ExecByRW(insertObject, object.BucketName, object.AccessKey, object.ContentName, object.Size, object.Cid, object.ContentType, object.ContentEncoding, object.Expires, object.Etag, object.Location)
	}
	return err
}

func DeleteContent(accessKey, bucketName, objectName string) error {
	_, err := postgres.ExecByRW(updateFolderDelete, accessKey, bucketName, objectName)
	return err
}

func DeleteObjects(accessKey, bucketName string, objectNames []string) error {

	placeholders := make([]string, len(objectNames))

	args := make([]interface{}, len(objectNames)+2)
	args[0] = accessKey
	args[1] = bucketName

	for i, o := range objectNames {
		placeholders[i] = fmt.Sprintf("$%d", i+3)
		args[i+2] = o
	}

	deleteSql := fmt.Sprintf(deleteObjects, "access_key=$1 and bucket_name=$2 and content_name in ("+strings.Join(placeholders, ",")+") and is_deleted=false")
	_, err := postgres.ExecByRW(deleteSql, args...)
	return err
}

func GetObject(accessKey, bucketName, objectName string) (*model.Object, error) {
	object := &model.Object{}
	err := postgres.GetByRO(getObject, object, accessKey, bucketName, objectName)
	if errors.Is(err, ErrNotFound) {
		return nil, nil
	}
	return object, err
}

func GetObjectByCid(cid string) (*model.Object, error) {
	object := &model.Object{}
	err := postgres.GetByRO(getObjectByCid, object, cid)
	if errors.Is(err, ErrNotFound) {
		return nil, nil
	}
	return object, nil
}

func ListObject(accessKey, bucketName string, prefix string) ([]*model.Object, error) {
	list := make([]*model.Object, 0)
	err := postgres.SelectByRO(queryFolderList, &list, accessKey, bucketName, prefix+"%")
	return list, err
}
