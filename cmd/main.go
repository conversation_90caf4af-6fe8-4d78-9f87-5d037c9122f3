package main

import (
	"context"
	"fmt"
	"gitlab.insidebt.net/btfs/storage3-backend/clean"
	"os"
	"time"
)

const (
	name    = "btfs-storage-backend"
	version = "alpha"
)

func init() {
	time.Local = time.UTC
}

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	clean.PushCancel(cancel)

	err := cmds.RunContext(ctx, os.Args)
	if err != nil {
		_, _ = fmt.Fprint(os.Stderr, err)
	}

	err = clean.Close()
	if err != nil {
		_, _ = fmt.Fprint(os.Stderr, err)
	}
}
