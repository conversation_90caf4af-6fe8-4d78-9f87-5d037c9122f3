package env

import (
	"fmt"
	"strings"
)

type Size int64

func parseHumanSize(hSize string) (size int64, err error) {
	var (
		num  int64
		unit string
	)
	_, err = fmt.Sscanf(hSize, "%d%s", &num, &unit)
	if err != nil {
		return
	}
	unit = strings.ToUpper(strings.TrimSpace(unit))
	switch unit {
	case "GB", "G":
		size = num * (1 << 30)
	case "MB", "M":
		size = num * (1 << 20)
	case "KB", "K":
		size = num * (1 << 10)
	case "B", "":
		size = num
	}
	return
}
