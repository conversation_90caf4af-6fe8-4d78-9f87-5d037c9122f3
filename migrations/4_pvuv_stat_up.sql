--1.添加字段
alter table tb_site
    add column current_signature_message varchar(2048) default '' not null;
alter table tb_deploy
    add column signature_message varchar(2048) default '' not null;
alter table tb_site
    add column once_signed bool default false not null;
alter table tb_site
    add column deployment_duration float default 0 not null;

--2.deploy表中，site只要有一个签名，site表once_signed字段需要set为true。
update tb_site as s
set once_signed= true
from (select site_id, count(1) as cnt
      from tb_deploy
      where length(signature) > 0
      group by site_id) as u
where s.site_id = u.site_id;

--3.site表设置deployment_duration（deploy表中，每个site最近一次成功的部署，用来计算部署时间差）
update tb_site as s
set deployment_duration=u.deployment_duration
from (select site_id, EXTRACT(EPOCH FROM (finished_at - created_at)) as deployment_duration
      from tb_deploy
      where id in
            (select max(id) as id
             from tb_deploy
             where status = 2
             group by site_id)) as u
where s.site_id = u.site_id;