package model

import "time"

type SiteVisitStat struct {
	Id        int64     `json:"id"`
	SiteId    string    `json:"site_id"`
	Domain    string    `json:"domain"`
	PV        int64     `json:"pv"`
	UV        int64     `json:"uv"`
	Bandwidth int64     `json:"bandwidth"`
	StatDate  time.Time `json:"stat_date"`
	FromTime  time.Time `json:"from_time"`
	ToTime    time.Time `json:"to_time"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type DomainVisitStat struct {
	PV        int64 `json:"pv"`
	UV        int64 `json:"uv"`
	Bandwidth int64 `json:"bandwidth"`
}
