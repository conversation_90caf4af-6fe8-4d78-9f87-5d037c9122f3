package hosting

import (
	"gitlab.insidebt.net/btfs/storage3-backend/model"
)

var svc Service

func init() {
	svc = &service{
		codeSourceDirPath: "/tmp/deploys",
	}
}

func QueryUserSiteList(user *model.User, transferringSiteList []string, arg *model.QuerySiteListArg) (list []*model.Site, total int64, err error) {
	return svc.QueryUserSiteList(user, transferringSiteList, arg)
}

func GetSiteLastVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error) {
	return svc.GetSiteLastVisitStatList(siteIdList)
}

func GetSite7daysVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error) {
	return svc.GetSite7daysVisitStatList(siteIdList)
}

func GetSiteLastNVisitDaysStatList(arg *model.GetSiteDailyVisitStatListArg) (list []*model.RespSiteDailyVisitStatList, err error) {
	return svc.GetSiteLastNVisitDaysStatList(arg)
}

func QueryUserSiteDetail(user *model.User, arg *model.QuerySiteDetailArg) (detail *model.Site, err error) {
	return svc.QueryUserSiteDetail(user, arg)
}

func UpdateUserSiteName(user *model.User, arg *model.UpdateSiteNameArg) (err error) {
	return svc.UpdateUserSiteName(user, arg)
}

func UpdateUserDeploySignature(user *model.User, arg *model.UpdateDeploySignatureArg) (err error) {
	return svc.UpdateUserDeploySignature(user, arg)
}

func UpdateSiteAutoDeploy(user *model.User, arg *model.UpdateSiteAutoDeployArg) (err error) {
	return svc.UpdateSiteAutoDeploy(user, arg)
}

func RegenerateSiteAutoDeploySecret(user *model.User, arg *model.RegenerateSiteAutoDeploySecretArg) (err error) {
	return svc.RegenerateSiteAutoDeploySecret(user, arg)
}

func UpdateUserSiteSetting(user *model.User, arg *model.UpdateSiteSettingArg) (err error) {
	return svc.UpdateUserSiteSetting(user, arg)
}

func DeleteUserSite(user *model.User, arg *model.DeleteSiteArg) (err error) {
	return svc.DeleteUserSite(user, arg)
}

func CreateUserSiteAndTriggerUserDeploy(user *model.User, arg *model.CreateSiteArg) (siteId, deployId string, err error) {
	return svc.CreateUserSiteAndTriggerUserDeploy(user, arg)
}

func DoDeploy(arg *model.DoDeployArg) (err error) {
	return svc.DoDeploy(arg)
}

func TriggerUserDeploy(user *model.User, arg *model.TriggerDeployArg) (siteId, deployId string, err error) {
	return svc.TriggerUserDeploy(user, arg)
}

func QueryUserDeployList(user *model.User, arg *model.QueryDeployListArg) (list []*model.DeployListItem, total int64, err error) {
	return svc.QueryUserDeployList(user, arg)
}

func GetSignedDeployList(arg *model.QueryDeployListArg) (list []*model.DeployListItemNonLogin, total int64, err error) {
	return svc.GetSignedDeployList(arg)
}

func QueryUserDeployDetail(user *model.User, arg *model.QueryDeployDetailArg) (detail *model.Deploy, err error) {
	return svc.QueryUserDeployDetail(user, arg)
}

func QueryUserDeployDetailByDeployId(deployId string) (detail *model.Deploy, err error) {
	return svc.QueryUserDeployDetailByDeployId(deployId)
}

func CancelUserDeploy(user *model.User, arg *model.CancelDeployArg) (err error) {
	return svc.CancelUserDeploy(user, arg)
}

func QueryPresetBuildSettingList() (list []*model.PresetBuildSetting, total int64, err error) {
	return svc.QueryPresetBuildSettingList()
}

func QuerySiteDetailBySiteName(arg *model.QuerySiteDetailBySiteNameArg) (detail *model.Site, err error) {
	return svc.QuerySiteDetailBySiteName(arg)
}

func QuerySiteDetailsBySiteNames(siteNames []string) (detail []*model.Site, err error) {
	return svc.QuerySiteDetailsBySiteNames(siteNames)
}

func QuerySiteDetailByRepo(repoArg *model.QuerySiteDetailByRepoArg) (detail []*model.Site, err error) {
	return svc.QuerySiteDetailByRepo(repoArg)
}

func QuerySiteDetailsBySiteIds(siteIds []string) (detail []*model.Site, err error) {
	return svc.QuerySiteDetailsBySiteIds(siteIds)
}

func IsUserInWhitelist(user *model.User) error {
	return svc.CheckUserInWhiteList(user)
}

func IsBuildImageAndCommandAllowed(buildImage, command string) error {
	return svc.CheckBuildImageAndCommand(buildImage, command)
}

func TransferSite(user *model.User, arg *model.TransferSiteArg) error {
	return svc.TransferSite(user, arg)
}

func AcceptOrDeclineSite(user *model.User, arg *model.TransferSiteOpArg) error {
	return svc.AcceptOrDeclineSite(user, arg)
}

func GetTransferringSite(user *model.User) (sites []*model.SiteTransferRecord, err error) {
	return svc.GetTransferringSite(user)
}

func GetSiteTransferHistory(siteId string) (sites []*model.SiteTransferRecord, err error) {
	return svc.GetSiteTransferHistory(siteId)
}

func GetTransferPendingSite(user *model.User) (sites []*model.SiteTransferRecord, err error) {
	return svc.GetTransferPendingSite(user)
}

func GetTransferringSiteBySiteId(siteId string, user *model.User, isLatest bool) (site *model.SiteTransferRecord, err error) {
	return svc.GetTransferringSiteBySiteId(siteId, user, isLatest)
}

func GetTransferAcceptedSite(siteIds []string, address string) (sites []*model.SiteTransferRecord, err error) {
	return svc.GetTransferAcceptedSiteBySiteIds(siteIds, address)
}
