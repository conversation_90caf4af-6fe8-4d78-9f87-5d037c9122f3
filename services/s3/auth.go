package s3

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"net/http"
	"net/url"
	"strings"
)

var (
	ErrAccessKeyIsValid    = errors.New("access key is invalid")
	ErrAccessKeyIsDisabled = errors.New("access key is disabled")
)

func getAccessKey(head http.Header) string {
	authorization := head.Get("Authorization")
	authField := strings.Split(strings.TrimSpace(strings.TrimPrefix(authorization, s3.SignV4Algorithm)), ",")
	credentials := strings.Split(strings.TrimPrefix(authField[0], "Credential="), "/")
	return credentials[0]
}

func getSecret(accessKey string) (string, string, error) {
	keyModel, err := data.Detail<PERSON>ey(context.Background(), accessKey)
	if err != nil {
		return "", "", err
	}

	if keyModel == nil || keyModel.IsDeleted {
		return "", "", ErrAccessKeyIsValid
	}
	if !keyModel.IsEnabled {
		return "", "", ErrAccessKeyIsDisabled
	}

	return keyModel.Secret, keyModel.Location, nil
}

func checkAndResetAuthorization(bizInfo *s3.BizInfo, request *http.Request) (*http.Request, string, error) {
	if bizInfo.AccessKey == "" {
		bizInfo.AccessKey = getAccessKey(request.Header)
	}

	secret, location, err := getSecret(bizInfo.AccessKey)

	if err != nil {
		return request, "", err
	}

	err = s3.ValidateSignature(request, secret)
	if err != nil {
		return request, "", err
	}

	base := config.BTFS.S3Host
	if config.BTFS.VisitType == s3.IP && location != "" {
		location = getLocationByKeyLocation(location)
		base = location
	}
	// set proxy host
	parsedUr, err := url.Parse(base)
	if err != nil {
		panic("config.btfs.s3host config error")
	}
	request.Host = parsedUr.Host

	// set request bucket name
	bucketName := bizInfo.BucketName
	if bucketName != "" {
		request.URL.Path = "/" + getBucketWithAccessKeyPrefix(bizInfo.AccessKey, bucketName)
	} else {
		request.URL.Path = "/"
	}

	if bizInfo.ObjectName != "" {
		request.URL.Path = request.URL.Path + "/" + bizInfo.ObjectName
	}

	// if copy set copy source
	if request.Header.Get("X-Amz-Copy-Source") != "" {
		request.Header.Set("X-Amz-Copy-Source", getBucketWithAccessKeyPrefix(bizInfo.AccessKey, request.Header.Get("X-Amz-Copy-Source")))
	}

	auth, err := s3.GenerationAuthorization(request, secret)
	if err != nil {
		return request, location, err
	}
	request.Header.Set("Authorization", auth)

	return request, location, nil
}

// Just for the conflict of different user's bucket
func getBucketWithAccessKeyPrefix(accessKey, bucketName string) string {
	delimiter := "-"
	prefix := getbucketPrefix(accessKey)
	// access_key 进行hash成16位的
	return prefix + delimiter + bucketName
}

func getbucketPrefix(accessKey string) string {
	hasher := md5.New()
	hasher.Write([]byte(accessKey))
	hash := hasher.Sum(nil)
	// 将前8个字节的哈希值转换为16个字符的十六进制字符串
	return hex.EncodeToString(hash[:8])
}
