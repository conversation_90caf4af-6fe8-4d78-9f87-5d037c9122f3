create table if not exists tb_bucket
(
    id                         bigserial primary key,
    bucket_name                character varying(512)      not null default ''::character varying,
    acl                        character varying(128)      not null default ''::character varying,
    region                     character varying(128)      not null default ''::character varying,
    access_key                 character varying(512)      not null default ''::character varying,
    location                   character varying(128)      not null default ''::character varying,
    is_deleted                 bool NOT NULL DEFAULT false,
    created_at                 timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at                 timestamp without time zone not null default (now() at time zone 'utc')
);
create index tb_bucket_access_key_bucket_name_idx on tb_bucket(access_key, bucket_name);

create table if not exists tb_key
(
    id                         bigserial primary key,
    user_id                    character varying(128)      not null default ''::character varying,
    access_key                 character varying(512)      not null default ''::character varying,
    secret                     character varying(128)      not null default ''::character varying,
    region                     character varying(128)      not null default ''::character varying,
    location                   character varying(128)      not null default ''::character varying,
    is_enabled                 bool NOT NULL DEFAULT true,
    is_deleted                 bool NOT NULL DEFAULT false,
    created_at                 timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at                 timestamp without time zone not null default (now() at time zone 'utc')
);
create index tb_key_access_key_idx on tb_key(access_key);

create table if not exists tb_object (
    id                         bigserial primary key,
    access_key                 character varying(512)      not null default ''::character varying,
    bucket_name                character varying(128)      not null default ''::character varying,
    content_type               character varying(128)      not null default ''::character varying,
    location                   character varying(128)      not null default ''::character varying,
    cid                        character varying(512)      not null default ''::character varying,
    content_name               character varying(512)      not null default ''::character varying,
    is_deleted                 bool NOT NULL DEFAULT false,
    size                       int8 NOT NULL DEFAULT 0,
    content_encoding           character varying(512)      not null default ''::character varying,
    expires                    timestamp without time zone not null,
    etag                       character varying(128)      not null default ''::character varying,
    created_at                 timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at                 timestamp without time zone not null default (now() at time zone 'utc')
);
create index tb_object_access_key_bucket_name_idx on tb_object(access_key, bucket_name);
