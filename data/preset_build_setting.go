package data

import "gitlab.insidebt.net/btfs/storage3-backend/model"

var presetBuildSettingList = []*model.PresetBuildSetting{
	{
		LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/cra.png",
		FrameWork:        "Create React App",
		BuildImage:       "btfs/create-react-app:latest",
		BuildCommand:     "yarn && yarn build",
		PublishDirectory: "/build",
		BaseDirectory:    "",
	},
	{
		LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/nextjs.png",
		FrameWork:        "NextJS",
		BuildImage:       "btfs/next-js:latest",
		BuildCommand:     "npm install && npm run build && npx next export",
		PublishDirectory: "/out",
		BaseDirectory:    "",
	},
	{
		LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/nextjs.png",
		FrameWork:        "node16-common-npm",
		BuildImage:       "btfs/common-node-16:latest",
		BuildCommand:     "npm install && npm run build",
		PublishDirectory: "/build",
		BaseDirectory:    "",
	},
	{
		LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/nextjs.png",
		FrameWork:        "node18-common-npm",
		BuildImage:       "btfs/common-node-18:latest",
		BuildCommand:     "npm install && npm run build",
		PublishDirectory: "/build",
		BaseDirectory:    "",
	},
	{
		LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/nextjs.png",
		FrameWork:        "node16-Common-yarn",
		BuildImage:       "btfs/common-node-16:latest",
		BuildCommand:     "yarn && yarn build",
		PublishDirectory: "/build",
		BaseDirectory:    "",
	},
	{
		LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/nextjs.png",
		FrameWork:        "node18-Common-yarn",
		BuildImage:       "btfs/common-node-18:latest",
		BuildCommand:     "yarn && yarn build",
		PublishDirectory: "/build",
		BaseDirectory:    "",
	},
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/hugo.png",
	//	FrameWork:        "Hugo",
	//	BuildImage:       "btfs/hugo-v0.85",
	//	BuildCommand:     "hugo",
	//	PublishDirectory: "public",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/gatsby.png",
	//	FrameWork:        "Gatsby",
	//	BuildImage:       "btfs/gatsby:node-16",
	//	BuildCommand:     "npm install && npm run build",
	//	PublishDirectory: "public",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/wp.png",
	//	FrameWork:        "Wordpress",
	//	BuildImage:       "",
	//	BuildCommand:     "",
	//	PublishDirectory: "",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/nuxtjs.png",
	//	FrameWork:        "NuxtJS",
	//	BuildImage:       "btfs/nuxtjs:node-16",
	//	BuildCommand:     "npm install && npm run build && npm run export",
	//	PublishDirectory: "dist",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/jekyll.png",
	//	FrameWork:        "Jekyll",
	//	BuildImage:       "btfs/jekyll",
	//	BuildCommand:     "jekyll build",
	//	PublishDirectory: "_site",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/gridsome.png",
	//	FrameWork:        "Gridsome",
	//	BuildImage:       "btfs/gridsome:node-16",
	//	BuildCommand:     "npm install && npm run build",
	//	PublishDirectory: "dist",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/svelte.png",
	//	FrameWork:        "Svelte",
	//	BuildImage:       "btfs/svelte:node-16",
	//	BuildCommand:     "npm install && npm run build",
	//	PublishDirectory: "public",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/mkdocs.png",
	//	FrameWork:        "MkDocs",
	//	BuildImage:       "btfs/mkdocs",
	//	BuildCommand:     "npm install && npm run build",
	//	PublishDirectory: "site",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/sapper.png",
	//	FrameWork:        "Svelte + Sapper",
	//	BuildImage:       "btfs/svelte:node-16",
	//	BuildCommand:     "npm install && npm run build",
	//	PublishDirectory: "__sapper__/export",
	//	BaseDirectory:    "",
	// },
	// {
	//	LogoUrl:          "https://storage.googleapis.com/terminal-assets/images/frameworks/wasm-pack.png",
	//	FrameWork:        "Wasm Pack",
	//	BuildImage:       "btfs/wasm-pack",
	//	BuildCommand:     "npm install && npm run build",
	//	PublishDirectory: "dist",
	//	BaseDirectory:    "",
	// },
}

func QueryPresetBuildSettingList() (list []*model.PresetBuildSetting, total int64, err error) {
	list = presetBuildSettingList
	total = int64(len(list))
	return
}
