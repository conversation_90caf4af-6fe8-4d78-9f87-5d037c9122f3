package github

import (
	"context"
	"net/http"

	"github.com/google/go-github/github"
)

type Service interface {
	ExchangeAccessToken(code string) (accessToken string, err error)
	GetGithubUser(accessToken string) (githubUser *github.User, resp *github.Response, err error)
	GetLatestCommitId(auth *Auth, repoOwner, repo, branch string) (commitId string, err error)
	FetchSource(ctx context.Context, auth *Auth, repoOwner, repo, commitId, dst string, sizeLimit int64) (root string, err error)
	ParseWebHook(r *http.Request) (body []byte, pushEvent *github.PushEvent, err error)
	ValidateSignature(signature string, body, secretKey []byte) error
}

type Auth struct {
	Id         int64
	OauthToken string
	Login      string
}
