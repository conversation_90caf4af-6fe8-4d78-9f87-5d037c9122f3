package handlers

import (
	"bytes"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
)

func Login(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	var arg model.LoginReq
	err = ctx.ShouldBind(&arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}
	// address store in lower case hex format in db
	if model.UserSource(arg.Source) == model.UserSourceTRON {
		arg.Address = tron.TronAddressToEtherAddress(arg.Address)
	}
	arg.Address = strings.ToLower(arg.Address)
	user, err := data.GetUserByAddr(arg.Address, arg.Source)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	var originStr = data.GetSignNonceInfo(user.SignNonce, user.Source)

	if user.Source == model.UserSourceBTTC {
		err = bttcLogin(arg.Address, originStr, arg.Signature)
	} else if user.Source == model.UserSourceTRON {
		err = tronLogin(arg.Address, originStr, arg.Signature)
	} else {
		err = errors.New("we just support the BTTC and TRON as the login source")
	}
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}
	err = cctx.SetLoginUser(ctx, user)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	// 签名成功后，更新sign_nonce字段
	err = data.UpdateUserSignNonce(user.Address)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	// update user info in redis session
	u, err := data.GetUserByUserId(user.UserId)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	err = cctx.SetLoginUser(ctx, u)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	response.DataOK(ctx, response.Data{
		"user": user,
	})
}

func bttcLogin(address, originStr, signature string) error {
	// web3.js has a stupid prefix, see https://web3js.readthedocs.io/en/v1.8.0/web3-eth-personal.html?#sign
	odata := []byte("\x19Ethereum Signed Message:\n" + fmt.Sprint(len(originStr)) + originStr)
	hash := crypto.Keccak256Hash(odata)
	b, err := hexutil.Decode(signature)
	if err != nil {
		return err
	}
	// https://github.com/ethereum/go-ethereum/issues/19751
	if len(b) != 65 {
		return errors.New("the length of signature is incorrect")
	}
	b[64] = b[64] - 27
	sigPublicKey, err := crypto.Ecrecover(hash.Bytes(), b)
	if err != nil {
		return err
	}
	pk, err := crypto.UnmarshalPubkey(sigPublicKey)
	if err != nil {
		return err
	}
	signAddress := crypto.PubkeyToAddress(*pk)
	if !strings.EqualFold(signAddress.Hex(), address) {
		return errors.New("sign check incorrect,please check your parameter")
	}
	return nil
}

func tronLogin(address, msg, sig string) error {
	const TRON_MESSAGE_PREFIX = "\x19TRON Signed Message:\n" // Define the prefix

	// Step 1: Compute the hash of the message
	messageLength := strconv.Itoa(len(msg)) // Convert message length to a string
	prefix := []byte(TRON_MESSAGE_PREFIX)
	lengthBytes := []byte(messageLength)
	messageBytes := []byte(msg)

	// Concatenate prefix, length, and message
	hashInput := bytes.Join([][]byte{prefix, lengthBytes, messageBytes}, []byte{})
	digestByte := crypto.Keccak256Hash(hashInput).Bytes()

	sigBytes, err := hexutil.Decode(sig)
	if err != nil {
		return errors.New("bad signature")
	}
	// https://github.com/ethereum/go-ethereum/issues/19751
	if len(sigBytes) != 65 {
		return errors.New("the length of signature is incorrect")
	}
	sigBytes[64] -= 27

	pubKeyRaw, err := crypto.Ecrecover(digestByte, sigBytes)
	if err != nil {
		return errors.New("bad signature, can not ecrecover")
	}
	pubKey, err := crypto.UnmarshalPubkey(pubKeyRaw)
	if err != nil {
		return errors.New("bad signature, can not unmarshal pubkey")
	}
	signerAddr := crypto.PubkeyToAddress(*pubKey)
	if !strings.EqualFold(signerAddr.Hex(), address) {
		return fmt.Errorf("sign check incorrect,please check your parameter. Signed address is:%s, your address is:%s", signerAddr.Hex(), address)
	}
	return nil
}

func Logout(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	err = cctx.DeleteLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	response.OK(ctx)
}
