package s3

import (
	"encoding/xml"
	"fmt"
	"github.com/tdewolff/parse/v2/buffer"
	"testing"
	"time"
)

func ParseXml(data []byte, target interface{}) error {
	encoder := xml.NewDecoder(buffer.NewReader(data))
	err := encoder.Decode(target)
	return err
}

func Test_parseXml(t *testing.T) {
	type args struct {
		data []byte
	}
	data := `<ListAllMyBucketsResult><Buckets><Bucket><CreationDate>2024-04-19T02:07:59.714Z</CreationDate><BucketName>test-bucket1</BucketName></Bucket><Bucket><CreationDate>2024-04-19T04:04:44.261Z</CreationDate><BucketName>test-bucket2</BucketName></Bucket></Buckets><Owner><DisplayName>6c508c07-02a2-46c3-a007-436a3fac3abf</DisplayName><ID>6c508c07-02a2-46c3-a007-436a3fac3abf</ID></Owner></ListAllMyBucketsResult>`

	type Bucket struct {
		CreationDate time.Time `xml:"CreationDate"`
		Name         string    `xml:"BucketName"`
	}

	type Owner struct{}

	type Resp struct {
		XMLName xml.Name `xml:"ListAllMyBucketsResult"`
		Buckets []Bucket `xml:"Buckets"`
	}

	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			args: args{data: []byte(data)},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &Resp{}
			err := ParseXml(tt.args.data, r)
			fmt.Println(r, err)
		})
	}
}
