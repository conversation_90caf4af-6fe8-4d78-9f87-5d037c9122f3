package tron

import (
	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/common"
	"strings"
)

func TronAddressToEtherAddress(tronAddr string) (etherAddr string) {
	hex, _, _ := base58.CheckDecode(tronAddr)
	etherAddr = "0x" + common.Bytes2Hex(hex)
	etherAddr = strings.ToLower(etherAddr)
	return
}

func Base58AddressToHexAddress(b58addr string) (hexAddr string) {
	hex, _, _ := base58.CheckDecode(b58addr)
	hex = append([]byte{0x41}, hex...)
	hexAddr = "0x" + common.Bytes2Hex(hex)
	hexAddr = strings.ToLower(hexAddr)
	return
}

func HexAddressToBase58Address(hexAddr string) (b58Addr string) {
	if strings.HasPrefix(hexAddr, "0x") || strings.HasPrefix(hexAddr, "0X") {
		hexAddr = hexAddr[2:]
	}
	if strings.HasPrefix(hexAddr, "41") {
		hexAddr = hexAddr[2:]
	}
	bts := common.Hex2Bytes(hexAddr)
	b58Addr = base58.CheckEncode(bts, 65)
	return
}
