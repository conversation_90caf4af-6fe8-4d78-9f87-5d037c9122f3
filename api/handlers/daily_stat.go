package handlers

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/services/stat"
)

func TriggerDailyStat(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	err = stat.DoDailyStat()
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.OK(ctx)
}

func GetDailyStatList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.QueryDailyStatListArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	list, err := stat.GetDailyStatList(arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"list": list,
	})
}

func GetSiteStatList(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	arg := &model.QuerySiteStatListArg{}
	err = ctx.ShouldBind(arg)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	list, err := stat.GetSiteStatList(arg)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"list": list,
	})
}
