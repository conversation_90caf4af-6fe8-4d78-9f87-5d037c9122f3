package model

import "time"

type Deploy struct {
	DeployId         string               `json:"deploy_id"`
	UserId           string               `json:"user_id"`
	SiteId           string               `json:"site_id"`
	SiteName         string               `json:"site_name"`
	Status           DeployStatus         `json:"status"`
	Domain           string               `json:"domain"`
	RepoOwner        string               `json:"repo_owner"`
	Repo             string               `json:"repo"`
	Branch           string               `json:"branch"`
	CommitId         string               `json:"commit_id"`
	FileHash         string               `json:"file_hash"`
	FileSize         int64                `json:"file_size"`
	BuildImage       string               `json:"build_image"`
	BuildCommand     string               `json:"build_command"`
	PublishDirectory string               `json:"publish_directory"`
	BaseDirectory    string               `json:"base_directory"`
	Log              string               `json:"log"`
	Summary          []*DeploySummaryItem `json:"summary"`
	CreatedAt        time.Time            `json:"created_at"`
	StartedAt        time.Time            `json:"started_at"`
	FinishedAt       time.Time            `json:"finished_at"`
	UpdatedAt        time.Time            `json:"updated_at"`
	Signature        string               `json:"signature"`
	SignatureMessage string               `json:"signature_message"`
}

type DeployListItem struct {
	DeployId         string       `json:"deploy_id"`
	SiteId           string       `json:"site_id"`
	Status           DeployStatus `json:"status"`
	Repo             string       `json:"repo"`
	RepoOwner        string       `json:"repo_owner"`
	Branch           string       `json:"branch"`
	CommitId         string       `json:"commit_id"`
	FileHash         string       `json:"file_hash"`
	FileSize         int64        `json:"file_size"`
	CreatedAt        time.Time    `json:"created_at"`
	StartedAt        time.Time    `json:"started_at"`
	FinishedAt       time.Time    `json:"finished_at"`
	Signature        string       `json:"signature"`
	SignatureMessage string       `json:"signature_message"`
}

type DeployListItemNonLogin struct {
	DeployId         string       `json:"deploy_id"`
	UserId           string       `json:"user_id"`
	SiteId           string       `json:"site_id"`
	SiteName         string       `json:"site_name"`
	Domain           string       `json:"domain"` //sign: domain
	Status           DeployStatus `json:"status"`
	Repo             string       `json:"repo"`       //sign: source(repo)
	RepoOwner        string       `json:"repo_owner"` //sign: source(repoOwner)
	Branch           string       `json:"branch"`
	CommitId         string       `json:"commit_id"` //sign: commit_id
	FileHash         string       `json:"file_hash"` //sign: cid
	CreatedAt        time.Time    `json:"created_at"`
	StartedAt        time.Time    `json:"started_at"`
	FinishedAt       time.Time    `json:"finished_at"` //sign: deployment_time
	Signature        string       `json:"signature"`
	SignatureMessage string       `json:"signature_message"`
}

type RespGetSignedDeployList struct {
	DeployListItemNonLogin
	Address        string     `json:"address"`
	Source         UserSource `json:"source"`
	GithubRepoPath string     `json:"github_repo_path"`
}

type DeployStatus int

const (
	DeployStatusPending DeployStatus = iota
	DeployStatusOngoing
	DeployStatusPublished
	DeployStatusOutdated
	DeployStatusCanceled
	DeployStatusFailed
	DeployStatusInsufficientStoreSize
	DeployStatusInsufficientSiteSize
	DeployStatusInsufficientSiteNum
	DeployStatusConfirmedStatus
)

type DeploySummaryItem struct {
	Title  string `json:"title"`
	Detail string `json:"detail"`
}

type TriggerDeployArg struct {
	SiteId string `json:"site_id" binding:"gte=1,lte=128"`
}

type DoDeployArg struct {
	UserId   string `json:"user_id"`
	SiteId   string `json:"site_id" binding:"gte=1,lte=128"`
	DeployId string `json:"deploy_id" binding:"gte=1,lte=128"`
}

type QueryDeployListArg struct {
	SiteId string `json:"site_id" binding:"gte=1,lte=128"`
	Limit  int64  `json:"limit" binding:"gte=1,lte=100"`
	Offset int64  `json:"offset" binding:"gte=0"`
}

type QueryDeployDetailArg struct {
	SiteId   string `json:"site_id" binding:"gte=1,lte=128"`
	DeployId string `json:"deploy_id" binding:"gte=1,lte=128"`
}

type CancelDeployArg struct {
	SiteId   string `json:"site_id" binding:"gte=1,lte=128"`
	DeployId string `json:"deploy_id" binding:"gte=1,lte=128"`
}
