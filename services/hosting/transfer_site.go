package hosting

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
)

var (
	ErrInvalidSignatureFormat = errors.New("invalid format of signature")
	ErrInvalidSignatureInfo   = errors.New("invalid info of signature")
)

type TransferSiteSignature struct {
	Operation string `json:"operation"`
	SiteName  string `json:"siteName"`
	SiteId    string `json:"siteId"`
	From      string `json:"from"`
	To        string `json:"to"`
	StartTime string `json:"startTime"`
	DueTime   string `json:"dueTime"`
}

type AcceptTransferSiteSignature struct {
	Operation   string `json:"operation"`
	SiteName    string `json:"siteName"`
	SiteId      string `json:"siteId"`
	From        string `json:"from"`
	To          string `json:"to"`
	ReceiptTime string `json:"receiptTime"`
}

func parseSignatureContent(s string) (*TransferSiteSignature, error) {
	signature := &TransferSiteSignature{}
	err := json.Unmarshal([]byte(s), signature)
	if err != nil {
		return nil, err
	}
	return signature, nil
}

func parseAcceptSignatureContent(s string) (*AcceptTransferSiteSignature, error) {
	signature := &AcceptTransferSiteSignature{}
	err := json.Unmarshal([]byte(s), signature)
	if err != nil {
		return nil, err
	}
	return signature, nil
}

func ValidateTransferSiteSignature(arg *model.TransferSiteArg, site *model.Site, user *model.User) error {
	signature, err := parseSignatureContent(arg.SignatureMessage)
	if err != nil {
		return ErrInvalidSignatureFormat
	}

	if signature.SiteId != arg.SiteId {
		fmt.Println(signature.SiteId, arg.SiteId)
		return ErrInvalidSignatureInfo
	}

	if signature.SiteName != fmt.Sprintf("%s.%s", site.SiteName, config.DNS.RootDomain) {
		fmt.Println(signature.SiteName, site.SiteName)
		return ErrInvalidSignatureInfo
	}

	fromAddress := ""
	if user.Source == model.UserSourceTRON {
		fromAddress = tron.HexAddressToBase58Address(user.Address)
	} else {
		fromAddress = user.Address
	}

	if strings.ToLower(signature.From) != strings.ToLower(fromAddress) {
		fmt.Println(signature.From, fromAddress)
		return ErrInvalidSignatureInfo
	}

	targetAddress := ""
	if model.UserSource(arg.TargetAddressType) == model.UserSourceTRON {
		targetAddress = tron.HexAddressToBase58Address(arg.TargetAddress)
	} else {
		targetAddress = arg.TargetAddress
	}

	if strings.ToLower(signature.To) != strings.ToLower(targetAddress) {
		fmt.Println(signature.To, targetAddress)
		return ErrInvalidSignatureInfo
	}

	return nil
}

func ValidateAcceptTransferSiteSignature(arg *model.TransferSiteOpArg, siteTransfer *model.SiteTransferRecord, site *model.Site) error {
	signature, err := parseAcceptSignatureContent(arg.SignatureMessage)
	if err != nil {
		return ErrInvalidSignatureFormat
	}

	fromAddress := ""
	if siteTransfer.FromAddressType == model.UserSourceTRON {
		fromAddress = tron.HexAddressToBase58Address(siteTransfer.FromAddress)
	} else {
		fromAddress = siteTransfer.FromAddress
	}
	toAddress := ""
	if siteTransfer.ToAddressType == model.UserSourceTRON {
		toAddress = tron.HexAddressToBase58Address(siteTransfer.ToAddress)
	} else {
		toAddress = siteTransfer.ToAddress
	}

	if signature.SiteId != arg.SiteId {
		return ErrInvalidSignatureInfo
	}

	if signature.SiteName != fmt.Sprintf("%s.%s", site.SiteName, config.DNS.RootDomain) {
		return ErrInvalidSignatureInfo
	}

	if strings.ToLower(signature.From) != strings.ToLower(fromAddress) {
		return ErrInvalidSignatureInfo
	}

	if strings.ToLower(signature.To) != strings.ToLower(toAddress) {
		return ErrInvalidSignatureInfo
	}

	return nil
}
