ACCESS=external

MIN_PODS=1
MAX_PODS=1
MIN_CPU=20m
MAX_CPU=200m
MIN_MEM=20Mi
MAX_MEM=200Mi

MIN_PODS_JOB=3
MAX_PODS_JOB=3
MIN_CPU_JOB=20m
MAX_CPU_JOB=2000m
MIN_MEM_JOB=20Mi
MAX_MEM_JOB=4000Mi

LOG_LEVEL="info"

DB_RW_USER="postgres"
DB_RW_PASSWORD="$POSTGRES_PWD_DEV"
DB_RW_HOST="$POSTGRES_HOST_DEV"
DB_RW_PORT="5432"
DB_RW_NAME="db_storage3"
DB_RW_MAX_OPEN_CONNS="32"
DB_RW_MAX_CONN_LIFE_TIME="1h"
DB_RW_MAX_CONN_IDLE_TIME="30m"

LOG_DB_NAME="db_btfs_gateway_log"

DB_RO_USER="postgres"
DB_RO_PASSWORD="$POSTGRES_PWD_DEV"
DB_RO_HOST="$POSTGRES_HOST_DEV"
DB_RO_PORT="5432"
DB_RO_NAME="db_storage3"
DB_RO_MAX_OPEN_CONNS="32"
DB_RO_MAX_CONN_LIFE_TIME="1h"
DB_RO_MAX_CONN_IDLE_TIME="30m"

REDIS_HOST="$REDIS_HOST_DEV"
REDIS_PASSWORD=""
REDIS_NETWORK="tcp"
REDIS_DB="0"
REDIS_MAX_IDLE_CONN="32"
REDIS_MAX_ACTIVE_CONN="32"
REDIS_CONN_IDLE_TIMEOUT="1m"
REDIS_CONN_WAIT="true"
REDIS_MAX_LIFE_TIME="1m"

SERVICE_PORT=5000

#BTFS_HOST="http://api.btfs-cluster:5001"
BTFS_HOST="http://btfs-nfthub-internet-4912acd91ec8de8a.elb.ap-southeast-1.amazonaws.com:5001"

BTFS_UPLOAD_API_PATH="/api/v1/add"
HOST_IPS="http://*************:5001"
#S3_HOST="http://api.btfs-cluster:6001"
S3_HOST="http://btfs-nfthub-internet-4912acd91ec8de8a.elb.ap-southeast-1.amazonaws.com:6001"

S3_HOST_IPS="http://*************:6001"
VISIT_TYPE="ip"

DNS_ROOT_DOMAIN="on.bttscan.net"
DNS_ZONE_ID="Z076524538CTIPKINFEGQ"

GITHUB_APP_ID="$GITHUB_APP_ID"
GITHUB_CLIENT_ID="$GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="$GITHUB_CLIENT_SECRET"
GITHUB_PRIVATE_KEY_PATH=""
GITHUB_ENDPOINT="https://github.com/login/oauth/access_token"

KAFKA_BOOTSTRAP_SERVERS="$KAFKA_DEV"

DEPLOY_JOB_TOPIC="t-storage3-deploy"
DEPLOY_JOB_GROUP_ID="g-storage3-deploy-v1.0"
DEPLOY_JOB_OFFSET_RESET="earliest"
DEPLOY_JOB_ROUTINES="2"

SITE_NAME_RESERVED_WORDS="tron,justin,sunyuchen,justinsun,sunjustin,tronscan,justlend,apenft,btfs,trongrid,winklink,juststable,btt,trx,usdd,usdj,usdt,usdc,tusd,jst,wallet,scan,sunswap,suncurve,www,stusdt"
SITE_NAME_RESERVED_WORDS_ALLOWED_USER_ADDRESSES="******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,TV6pAYXCdVYcbW3Vtm8cnRv6ErPjjPcYym:TRON,TJuQa3zWp13AFQc9UbqzuqNMPh6ie3BXcR:TRON"
USER_ADDRESSES_WHITELIST="******************************************:TRON,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:TRON,******************************************:TRON,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,0x284f47d5d7f61729fa2b3899129a082428346e67:BTTC,******************************************:TRON,0x63533267e39a25a343eb2274b22511a36a76649b:TRON,0x815cee25bce27b2b27a3736b0d6c8a24ad4324d0:TRON,0x01ebb665df37d0ef6ec08ff13835f1bc2b50d3ab:TRON,0x18cbf9f856af51e7c8793a88d7dc211c0aa86c37:BTTC,0x26b239e31fdac33142188b2aa77faf4642859dea:TRON,0x6d4eF1b18C7405d57e5c684de020a5169b876F9a:TRON"


TRON_API_HOST="https://api.trongrid.io"
TRON_API_KEY="1a42490a-07f7-421a-83ce-63269d35b3a9"


PROFITS_SUPER_USER_ADDRESSES="0xbbbe0c29d361868da6ef25e11d07c1ba45fd8f77:BTTC,0x9867ec4cc7666842c8c2dbbaa880a655ea27d81f:TRON,TDVpBiWtqDXk6xPyPavtRACF2krh3zxJEm:TRON"
PROFITS_FREE_STORE_SIZE="150MB"
PROFITS_GOLD_NFT_HOLDER_STORE_SIZE="100MB"
PROFITS_SILVER_NFT_HOLDER_STORE_SIZE="85MB"
PROFITS_BRONZE_NFT_HOLDER_STORE_SIZE="75MB"
PROFITS_FREE_SITE_SIZE="100MB"
PROFITS_GOLD_HOLDER_SITE_SIZE="1GB"
PROFITS_SILVER_NFT_HOLDER_SITE_SIZE="800MB"
PROFITS_BRONZE_NFT_HOLDER_SITE_SIZE="600MB"
PROFITS_FREE_SITE_NUM="60"
PROFITS_GOLD_NFT_HOLDER_SITE_NUM="200"
PROFITS_SILVER_NFT_HOLDER_SITE_NUM="150"
PROFITS_BRONZE_NFT_HOLDER_SITE_NUM="100"

AWS_OUTPUT_BUCKET="s3://btfs-logs/storage3/"
AWS_REGION="ap-southeast-1"

DOCKER_HOST="tcp://**************:2375"
AWS_S3_BUCKET=for-docker-hongkong
AWS_S3_ACCESS_ID=********************
AWS_S3_SECRET_ACCESS_KEY=pIvkfO+ck4jsbZmiCvA+04HKYH6Zkz20bt+21UIa
