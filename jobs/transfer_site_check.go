package jobs

import (
	"context"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
)

// 检查超时没有操作的转移中的站点，如果超时没有接收或者拒绝，就将改记录置为过期

func DoTransferSiteCheck() {
	data.UpdatePendingRecord(24 * 60 * time.Minute)
}

func StartTransferSiteCheckJob(ctx context.Context) (err error) {
	s := gocron.NewScheduler(time.UTC)
	job, err := s.Every(10 * time.Minute).Do(DoTransferSiteCheck)
	if err != nil {
		return
	}
	job.SingletonMode()
	s.StartAsync()
	<-ctx.Done()
	s.Stop()
	return
}
