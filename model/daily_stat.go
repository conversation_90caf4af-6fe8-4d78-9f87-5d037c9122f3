package model

import "time"

type DailyStat struct {
	Id                int64     `json:"id"`
	StatDate          time.Time `json:"stat_date"`
	UserTotal         int64     `json:"user_total"`
	SiteTotal         int64     `json:"site_total"`
	SiteSizeTotal     int64     `json:"site_size_total"`
	SiteSizeUserTotal int64     `json:"site_size_user_total"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

type QueryDailyStatListArg struct {
	From *time.Time `json:"from" form:"from"`
	To   *time.Time `json:"to" form:"to"`
}
