package s3

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"testing"
)

func TestAaa(t *testing.T) {
	a := "folder"
	b := strings.Split(a, "/")
	fmt.Println(b)
	c := "folder1/"
	d := strings.Split(c, "/")
	fmt.Println(d[0], d[1])

	// ids := []int{1, 2, 3}
	// query := fmt.Sprintf("SELECT * FROM mytable WHERE id IN (%v)", ids)
	// fmt.Println(query)
	//
	// ids = append(ids, 4)
	// ids = append(ids, 5)
	// fmt.Println(ids)

	ids := []int{1, 2, 3}
	placeholders := make([]string, len(ids))
	args := make([]interface{}, len(ids))
	for i, id := range ids {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}

	query := fmt.Sprintf("SELECT * FROM mytable WHERE id IN (%s)", strings.Join(placeholders, ","))
	fmt.Println(query)

	ids = []int{1, 2, 3}
	placeholders = make([]string, len(ids))
	args = make([]interface{}, len(ids))
	for i, id := range ids {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}

	query = fmt.Sprintf("DELETE FROM mytable WHERE id IN (%s)", strings.Join(placeholders, ","))
	fmt.Println(query)
}

func TestEscape(t *testing.T) {
	a := "http://127.0.0.1:6001/ef935c08-4e70-4ca5-bdef-4028ca0389ed-bucket1/folder2%252Faa%252Faa.txt?x-id=PutObject"
	a, err := url.PathUnescape(a)
	fmt.Println(a, err)

}

func Test_Authorization(t *testing.T) {

	request := &http.Request{}

	request.Method = http.MethodGet

	request.URL = &url.URL{
		Scheme: "http",
		Host:   "storage3-dev.btfs.io",
		Path:   "/09125e87-d187-4313-89a3-eaa3a8c1ce87-bucket1/",
	}
	request.URL.RawQuery = "delimiter=%2F&prefix="

	request.Header = http.Header{}
	request.Header.Set(
		"Authorization",
		"AWS4-HMAC-SHA256 Credential=cbf25977-dff3-4fe9-9746-70429f4a008c/20240618/us-east-1/s3/aws4_request, SignedHeaders=amz-sdk-invocation-id;amz-sdk-request;host;x-amz-content-sha256;x-amz-date;x-amz-user-agent, Signature=35a9686ba24880e0fc222c3db6a6fede6fcdd3d984d32736010f8e09089c440d",
	)

	// amz-sdk-invocation-id;
	request.Header.Set("amz-sdk-invocation-id", "44f31857-5c2f-41e7-af8f-027e03258bbd")
	// amz-sdk-request;
	request.Header.Set("amz-sdk-request", "attempt=1; max=3")
	// content-type;
	request.Header.Set("content-type", "application/xml")
	// host;
	request.Header.Set("host", "127.0.0.1:6001")

	// x-amz-content-sha256;
	request.Header.Set("x-amz-content-sha256", "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855")
	// x-amz-user-agent
	request.Header.Set("x-amz-user-agent", "aws-sdk-js/3.418.0 ua/2.0 os/Windows#NT-10.0 lang/js md/browser#Chrome_126.0.0.0 api/s3#3.418.0")
	// x-amz-date;
	request.Header.Set("x-amz-date", "20240618T063241Z")

	secret := "36KXmTnQsEMrxpJlCo8ds0ibDs8p1JBJ"

	sig, err := getAuthorization(request, secret)
	if err != nil {
		panic(err)
	}
	auth, err := unMarshall(request.Header.Get("Authorization"))
	if auth.Signature != sig.Signature {
		fmt.Println(auth.Signature)
		fmt.Println(sig.Signature)
		t.Fatal("not match")
	}
}
