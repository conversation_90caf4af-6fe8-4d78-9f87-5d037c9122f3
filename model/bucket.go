package model

import (
	"time"
)

type BucketReq struct {
	BucketName string `uri:"bucket_name" json:"name" binding:"required"`
}

type FolderReq struct {
	BucketName string `json:"bucket_name" binding:"required"`
	FolderName string `json:"folder_name" binding:"required"`
}

type ListFolderReq struct {
	BucketName string `json:"bucket_name" binding:"required"`
	FolderName string `json:"folder_name"`
}

type FileReq struct {
	Key        string `json:"key" binding:"required"`
	BucketName string `json:"bucket_name" binding:"required"`
	FolderName string `json:"folder_name"`
	FileName   string `json:"file_name" binding:"required"`
}

type RenameFileReq struct {
	Key             string `json:"key" binding:"required"`
	BucketContentId int    `json:"bucket_content_id" binding:"required"`
	BucketName      string `json:"bucket_name" binding:"required"`
	FolderName      string `json:"folder_name"`
	FileName        string `json:"file_name" binding:"required"`
	OldFileName     string `json:"old_file_name" binding:"required"`
}

// db model

type Bucket struct {
	Id         int
	BucketName string
	Acl        string
	Region     string
	Location   string
	AccessKey  string
	IsDeleted  bool
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type Object struct {
	Id              int       `json:"id,omitempty"`
	AccessKey       string    `json:"access_key"`
	BucketName      string    `json:"bucket_name,omitempty"`
	ContentName     string    `json:"content_name,omitempty"`
	Size            int64     `json:"size"`
	Cid             string    `json:"cid,omitempty"`
	Location        string    `json:"location"`
	ContentType     string    `json:"content_type,omitempty"`
	ContentEncoding string    `json:"content_encoding"`
	Expires         time.Time `json:"expires"`
	Etag            string    `json:"etag"`
	IsDeleted       bool      `json:"is_deleted,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}
