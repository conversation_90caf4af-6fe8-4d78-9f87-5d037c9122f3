create table if not exists tb_site_domain_history
(
    id         bigserial primary key,
    site_id    character varying(128)      not null default ''::character varying,
    domain     character varying(128)      not null default ''::character varying,
    from_time  timestamp without time zone not null default (now() at time zone 'utc'),
    to_time    timestamp without time zone not null default (now() at time zone 'utc'),
    created_at timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at timestamp without time zone not null default (now() at time zone 'utc'),
    unique (site_id, from_time)
);

-- 从站点表中构建站点域名历史数据，域名起始时间为站点创建时间
insert into tb_site_domain_history (site_id, domain, from_time, to_time)
    select site_id, domain, created_at as from_time, '2100-01-01' as to_time
    from tb_site
    where is_deleted = 0;
