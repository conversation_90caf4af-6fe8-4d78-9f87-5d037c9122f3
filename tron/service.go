package tron

import (
	"context"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var service NFTService

func init() {
	service = newNft(newChain(config.Tron.APIHost, config.Tron.APIKey, config.Tron.IntervalMs))
}

func GetNFTTransferEvents(ctx context.Context, arg *GetNFTTransferEventsArg) (rsp *GetNFTTransferEventsResponseBody, err error) {
	return service.GetNFTTransferEvents(ctx, arg)
}

func GetNFTTokenUri(ctx context.Context, arg *GetNFTTokenUriArg) (rsp *GetNFTTokenUriResponseBody, err error) {
	return service.GetNFTTokenUri(ctx, arg)
}
