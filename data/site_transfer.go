package data

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

const insertSiteTransferRecordSql = `
insert into tb_site_transfer_record
	(
		site_id, from_user_id, from_address, from_signature, from_address_type, 
	 to_address, to_user_id, to_address_type,from_signature_message, transfer_status,created_at
	)
values
	(
		$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
	)
`

func InsertSiteTransferRecord(site *model.SiteTransferRecord) (err error) {
	_, err = postgres.ExecByRW(
		insertSiteTransferRecordSql,
		site.SiteId, site.FromUserId, site.FromAddress, site.FromSignature, site.FromAddressType,
		site.ToAddress, site.ToUserId, site.ToAddressType, site.FromSignatureMessage, site.TransferStatus, site.CreatedAt,
	)
	return
}

const updateTransferSiteStatusSql = `update tb_site_transfer_record set 
	transfer_status = $1, to_signature = $2, to_signature_message = $3, updated_at = (now() at time zone 'utc')
where
	site_id = $4 and transfer_status = $5 and to_address = $6 
`

func UpdateSiteTransferRecordStatus(site *model.SiteTransferRecord) (err error) {
	tg, err := postgres.ExecByRW(
		updateTransferSiteStatusSql, site.TransferStatus, site.ToSignature,
		site.ToSignatureMessage, site.SiteId, 1, site.ToAddress)
	updated := tg.RowsAffected() > 0
	if !updated {
		err = errors.New("transfer record doesn't exists")
	}
	return
}

const deleteTransferSiteSql = `update tb_site_transfer_record set
	transfer_status = $1, updated_at = (now() at time zone 'utc')
where
	site_id = $2 and transfer_status = $3 and to_address = $4
`

// 从接受状态 转移到 创建状态
func DeleteTransferSite(siteId string, toAddress string, transferStatus int) (err error) {
	affected, err := postgres.ExecByRW(deleteTransferSiteSql, model.TransferStatusAcceptedAndCreated, siteId, transferStatus, toAddress)
	if err != nil {
		return err
	}
	if affected.RowsAffected() == 0 {
		return errors.New("transferring site doesn't exists")
	}
	return
}

// ExpireTransferSiteRecord 从 pending 状态转移 expire
func ExpireTransferSiteRecord(siteId string, toAddress string) (err error) {
	affected, err := postgres.ExecByRW(deleteTransferSiteSql, model.TransferStatusExpired, siteId, model.TransferStatusPending, toAddress)
	if err != nil {
		return err
	}
	if affected.RowsAffected() == 0 {
		return errors.New("transferring site doesn't exists")
	}
	return
}

const queryPendingSiteTransferRecordSql = `select * from tb_site_transfer_record where site_id = $1 and to_address = $2 and transfer_status = 1`

func GetSitePendingTransferRecord(siteId string, userAddress string) (site *model.SiteTransferRecord, err error) {
	site = &model.SiteTransferRecord{}
	err = postgres.GetByRW(queryPendingSiteTransferRecordSql, site, siteId, userAddress)
	return
}

const querySiteTransferHistorySql = `select * from tb_site_transfer_record where site_id = $1 and (transfer_status = $2 or transfer_status = $3) order by created_at desc`

func GetSiteTransferHistory(siteId string) (site []*model.SiteTransferRecord, err error) {
	err = postgres.SelectByRW(querySiteTransferHistorySql, &site, siteId, model.TransferStatusAccepted, model.TransferStatusAcceptedAndCreated)
	return
}

const queryTransferringSiteSql = `select * from tb_site_transfer_record where (from_user_id = $1 or to_user_id = $2) and transfer_status = 1`

func GetTransferringSite(userId string) (sites []*model.SiteTransferRecord, err error) {
	err = postgres.SelectByRO(queryTransferringSiteSql, &sites, userId, userId)
	return
}

// 别人转给他  他又转给了别人
const queryTransferringSiteWithOutSiteIdSql = `select * from tb_site_transfer_record where (transfer_status=1 and (to_address=$1 or from_user_id=$2))`

func GetPendingSiteByUserId(address string, userId string) ([]*model.SiteTransferRecord, error) {
	sites := make([]*model.SiteTransferRecord, 0)
	err := postgres.SelectByRO(queryTransferringSiteWithOutSiteIdSql, &sites, address, userId)
	return sites, err
}

const queryAcceptedSiteSql = `select * from tb_site_transfer_record where site_id in (%s)  and transfer_status=2 and to_address=$1`

func GetAcceptedSiteBySiteIds(siteIds []string, address string) (sites []*model.SiteTransferRecord, err error) {
	err = postgres.SelectByRO(fmt.Sprintf(queryAcceptedSiteSql, strings.Join(siteIds, ",")), &sites, address)
	if err != nil {
		return nil, err
	}
	return
}

// 查找最新的一条数据
const queryTransferringSiteBySiteIdSql = `select * from tb_site_transfer_record
    where site_id = $1 and (to_address=$2 or from_user_id=$3) order by id desc limit 1`

const queryTransferringSiteBySiteIdSql2 = `select * from tb_site_transfer_record
    where site_id = $1 and (to_address=$2 or from_user_id=$3) order by id desc limit 2`

func GetTransferringSiteBySiteId(siteId string, address string, userId string, isLatest bool) (site *model.SiteTransferRecord, err error) {
	if isLatest {
		site = &model.SiteTransferRecord{}
		err = postgres.GetByRO(queryTransferringSiteBySiteIdSql, site, siteId, address, userId)
		return
	}

	sites := make([]*model.SiteTransferRecord, 0)
	err = postgres.SelectByRO(queryTransferringSiteBySiteIdSql2, &sites, siteId, address, userId)
	if err == nil && len(sites) >= 2 {
		site = sites[1]
	}
	return
}

const updatePendingRecordToExpired = `update tb_site_transfer_record set transfer_status = 4 where transfer_status = 1 and created_at < $1`

func UpdatePendingRecord(duration time.Duration) {
	expireTime := time.Now().Add(-duration)
	affected, err := postgres.ExecByRW(updatePendingRecordToExpired, expireTime)
	if err != nil {
		fmt.Println("UpdatePendingRecord err = ", err)
		return
	}
	if affected.RowsAffected() == 0 {
		fmt.Println("pending site doesn't exists")
		return
	}
	return
}
