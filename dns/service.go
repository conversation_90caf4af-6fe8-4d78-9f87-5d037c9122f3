package dns

import "gitlab.insidebt.net/btfs/storage3-backend/config"

var svc Service

func init() {
	var err error
	svc, err = newService(config.DNS.RootDomain, config.DNS.ZoneId)
	if err != nil {
		panic(err)
	}
}

func GetRootDomain() string {
	return svc.GetRootDomain()
}

func UpsertDNSLinkRecord(subdomain string, dnslink string) (changeInfo ChangeInfo, err error) {
	return svc.UpsertDNSLinkRecord(subdomain, dnslink)
}

func DeleteDNSLinkRecord(subdomain string, dnslink string) (changeInfo ChangeInfo, err error) {
	return svc.DeleteDNSLinkRecord(subdomain, dnslink)
}
