package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
)

// 设置 Gin 路由
func setupRouter() *gin.Engine {
	r := gin.Default()
	r.POST("/transfer_site", TransferSite)
	return r
}
func TestTransferSite(t *testing.T) {
	r := setupRouter()

	type args struct {
		req model.TransferSiteArg
	}

	tests := []struct {
		name    string
		args    args
		errCode response.Code
	}{
		{
			name: "siteId empty",
			args: args{
				req: model.TransferSiteArg{},
			},
			errCode: response.CodeParamErr,
		},
		{
			name: "siteId invalid",
			args: args{
				req: model.TransferSiteArg{
					SiteId:           "invalid",
					SignatureMessage: "hello",
					Signature:        "hello",
					TargetAddress:    "hello",
				},
			},
			errCode: response.CodeParamErr,
		},
	}

	for _, tt := range tests {
		w := httptest.NewRecorder()
		t.Run(tt.name, func(t *testing.T) {
			b, _ := json.Marshal(tt.args.req)
			req, _ := http.NewRequest("POST", "/transfer_site", bytes.NewBuffer(b))
			r.ServeHTTP(w, req)
			res := &response.Response{}
			_ = json.Unmarshal(w.Body.Bytes(), res)
			assert.Equal(t, tt.errCode, res.Code)
		})
	}

}
