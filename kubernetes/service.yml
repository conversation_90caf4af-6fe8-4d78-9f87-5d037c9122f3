---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: "60"
  name: $SERVICE_NAME-service
  namespace: $NAMESPACE
  labels:
    app: $SERVICE_NAME-service
    version: v1
spec:
  ports:
    - name: http
      port: $SERVICE_PORT
      targetPort: http
    - name: metrics
      port: $METRICS_PORT
      targetPort: metrics
  selector:
    app: $SERVICE_NAME
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $SERVICE_NAME
  namespace: $NAMESPACE
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: $SERVICE_NAME
  template:
    metadata:
      name: $SERVICE_NAME
      labels:
        app: $SERVICE_NAME
        version: v1
    spec:
      containers:
        - name: $SERVICE_NAME
          image: $IMAGE_NAME
          imagePullPolicy: Always
          resources:
            requests:
              memory: "$MIN_MEM"
              cpu: "$MIN_CPU"
            limits:
              memory: "$MAX_MEM"
              cpu: "$MAX_CPU"
          ports:
            - name: http
              containerPort: $SERVICE_PORT
            - name: metrics
              containerPort: $METRICS_PORT
          envFrom:
            - configMapRef:
                name: $SERVICE_NAME-configmap
          tty: true
          stdin: true
          securityContext:
            privileged: true
      securityContext:
        fsGroup: 1000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $SERVICE_NAME-jobs
  namespace: $NAMESPACE
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: $SERVICE_NAME-jobs
  template:
    metadata:
      name: $SERVICE_NAME-jobs
      labels:
        app: $SERVICE_NAME-jobs
        version: v1
    spec:
      shareProcessNamespace: true
      terminationGracePeriodSeconds: 120
      containers:
        - name: $SERVICE_NAME-jobs
          image: $IMAGE_NAME
          args:
            - -c
            - a=1; while test $a -ne 0; do echo -e 'waiting for docker daemon ready...';
              sleep 1; echo 'exit' | telnet localhost 2375 >/dev/null 2>&1; a=$?; done;
              echo -e 'docker daemon is ready!'; exec ./storage3 run_jobs
          command:
            - /bin/sh
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: "$MIN_MEM_JOB"
              cpu: "$MIN_CPU_JOB"
            limits:
              memory: "$MAX_MEM_JOB"
              cpu: "$MAX_CPU_JOB"
          envFrom:
            - configMapRef:
                name: $SERVICE_NAME-configmap
          volumeMounts:
            - name: deploys
              mountPath: /deploys
          tty: true
          stdin: true
        - name: docker
          image: docker:20-dind
          args:
          - -c
          - $(while test $(iptables-save | grep docker | wc -l) -le 0; do iptables-save > iptables.txt; sleep 1; done; iptables -I FORWARD -i docker0 -d **********/16 -j DROP; iptables -I FORWARD -i docker0 -d ***************/32 -j DROP) & exec dockerd-entrypoint.sh
          command:
          - /bin/sh
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - while test $(ps -ef | grep storage3 | grep -v grep | wc -l) -gt 0; do echo 'waiting storage3 exit...'; sleep 1; done
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: "$MIN_MEM_JOB"
              cpu: "$MIN_CPU_JOB"
            limits:
              memory: "$MAX_MEM_JOB"
              cpu: "$MAX_CPU_JOB"
          env:
            - name: DOCKER_TLS_CERTDIR
          volumeMounts:
            - name: docker-storage
              mountPath: /var/lib/docker
            - name: deploys
              mountPath: /deploys
          tty: true
          stdin: true
          securityContext:
            privileged: true
      volumes:
        - name: docker-storage
          emptyDir: { }
        - name: deploys
          emptyDir: { }
      securityContext:
        fsGroup: 1000
