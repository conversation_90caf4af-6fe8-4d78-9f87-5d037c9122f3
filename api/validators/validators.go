package validators

import (
	"github.com/go-playground/validator/v10"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"path"
)

var validators = map[string]func(level validator.FieldLevel) bool{
	"sitename": func(fl validator.FieldLevel) bool {
		return model.IsValidSiteName(fl.Field().String())
	},
	"abspath": func(f1 validator.FieldLevel) bool {
		v := f1.Field().String()
		return v == "" || path.IsAbs(v)
	},
}
