package docker

import (
	"bufio"
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/docker/docker/api/types/mount"

	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
	config2 "gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
)

type service struct {
	cli   *client.Client
	s3Cli *s3.S3
}

func newService() (s *service, err error) {
	s = new(service)
	s.cli, err = client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return nil, fmt.Errorf("failed to create Docker client: %w", err)
	}

	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(config2.AwsS3.Region),
		Credentials: credentials.NewStaticCredentials(config2.AwsS3.AccessId, config2.AwsS3.SecretAccessKey, ""),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create S3 client: %w", err)
	}

	s.s3Cli = s3.New(sess)

	return s, nil
}

func (s *service) EnsureImage(image string, handlerLog func(line string)) (err error) {
	reader, err := s.cli.ImagePull(context.Background(), image, types.ImagePullOptions{})
	if err != nil {
		return
	}
	defer reader.Close()
	rdr := bufio.NewScanner(reader)
	for rdr.Scan() {
		line := rdr.Text()
		handlerLog(line)
	}
	return
}

func (s *service) HandleContainerLog(ctx context.Context, id string, handler func(line string)) (err error) {
	reader, err := s.cli.ContainerLogs(
		ctx, id, container.LogsOptions{
			Follow:     true,
			ShowStdout: true,
			ShowStderr: true,
		},
	)
	if err != nil {
		return
	}
	defer reader.Close()
	rdr := bufio.NewScanner(reader)

	for {
		select {
		case <-ctx.Done():
			err = ctx.Err()
			return
		default:
		}
		if ok := rdr.Scan(); ok {
			line := rdr.Text()
			handler(line)
		} else {
			break
		}
	}

	err = rdr.Err()
	return
}

func (s *service) ContainerRun(ctx context.Context, image string, workDir string, command []string, mounts []Mount, handlerLog func(line string)) (id string, err error) {
	err = s.EnsureImage(image, handlerLog)
	if err != nil {
		return
	}

	hostConfig := container.HostConfig{}
	tMounts := make([]mount.Mount, len(mounts))
	for i, m := range mounts {
		tMounts[i] = mount.Mount(m)
	}
	hostConfig.Mounts = tMounts

	resp, err := s.cli.ContainerCreate(
		ctx, &container.Config{
			Tty:        true,
			Image:      image,
			WorkingDir: workDir,
			Cmd:        command,
			User:       "1000:1000",
		},
		&hostConfig,
		nil,
		nil,
		"",
	)

	if err != nil {
		err = fmt.Errorf("create container: %v", err)
		return "", err
	}

	err = s.cli.ContainerStart(ctx, resp.ID, container.StartOptions{})
	if err != nil {
		err = fmt.Errorf("start container: %v", err)
		return "", err
	}

	return resp.ID, nil
}

func (s *service) ContainerWait(ctx context.Context, id string) (state int64, err error) {
	resultC, errC := s.cli.ContainerWait(ctx, id, "")
	select {
	case err = <-errC:
		err = fmt.Errorf("wait container: %v", err)
	case result := <-resultC:
		state = result.StatusCode
	}
	return
}

func (s *service) ContainerRunAndClean(ctx context.Context, image, workDir string, command []string, mounts []Mount, handlerLog func(line string)) (statusCode int64, err error) {
	// Start the container
	id, err := s.ContainerRun(ctx, image, workDir, command, mounts, handlerLog)
	if err != nil {
		return
	}

	defer func() {
		rerr := s.cli.ContainerRemove(context.Background(), id, container.RemoveOptions{
			RemoveVolumes: true,
			Force:         true,
		})
		if rerr != nil {
			log.WithFields(log.Fields{
				"module": "remove_container_s",
				"id":     id,
			}).Error(rerr)
		}
	}()

	wg := &sync.WaitGroup{}
	defer wg.Wait()

	wg.Add(1)
	go func() {
		defer wg.Done()
		herr := s.HandleContainerLog(ctx, id, handlerLog)
		if herr != nil {
			log.WithFields(log.Fields{
				"module": "handle_container_log",
				"id":     id,
			}).Error(err)
		}
	}()

	// Wait for it to finish
	statusCode, err = s.ContainerWait(ctx, id)
	return
}

func (s *service) MountVolume(path, deployID string) error {
	return uploadDirToS3(s.s3Cli, config2.AwsS3.Bucket, "btfs-storage", path, deployID)
}

func (s *service) UnmountVolume(path, deployID string) error {
	return deleteDirFromS3(s.s3Cli, config2.AwsS3.Bucket, "btfs-storage/"+deployID)
}

func (s *service) CopyVolume(path, deployID, publishDir string) error {
	return downloadDirFromS3(s.s3Cli, config2.AwsS3.Bucket, "btfs-storage", path, deployID, publishDir)
}

func uploadDirToS3(client *s3.S3, bucketName, s3Prefix, localDir, deployID string) error {
	return filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		relPath := strings.TrimPrefix(path, localDir)
		relPath = strings.TrimPrefix(relPath, string(filepath.Separator))

		s3Key := filepath.Join(s3Prefix, deployID, relPath)

		if !info.IsDir() {
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()

			_, err = client.PutObject(&s3.PutObjectInput{
				Bucket: aws.String(bucketName),
				Key:    aws.String(s3Key),
				Body:   file,
				// ACL:    s3Types.ObjectCannedACLPublicRead,
			})
			if err != nil {
				return err
			}
			log.Infof("上传文件: %s 到 S3: %s\n", path, s3Key)
		}
		return nil
	})
}

func deleteDirFromS3(client *s3.S3, bucketName, folderPrefix string) error {

	var continuationToken *string = nil

	for {
		listInput := &s3.ListObjectsV2Input{
			Bucket:            aws.String(bucketName),
			Prefix:            aws.String(folderPrefix),
			ContinuationToken: continuationToken,
		}
		result, err := client.ListObjectsV2(listInput)
		if err != nil {
			log.Errorf("failed to list objects: %v", err)
			return err
		}

		if len(result.Contents) == 0 {
			break
		}

		for _, item := range result.Contents {
			_, err = client.DeleteObject(&s3.DeleteObjectInput{Bucket: aws.String(bucketName), Key: item.Key})
			if err != nil {
				log.Errorf("failed to delete objects: %v", err)
				return err
			}

			if result.IsTruncated != nil && *result.IsTruncated {
				continuationToken = result.NextContinuationToken
			} else {
				break
			}
		}
	}

	log.Infof("Successfully deleted all objects under prefix: %s", folderPrefix)

	return nil
}

func downloadDirFromS3(client *s3.S3, bucketName, s3Prefix, localDir, deployID, publishDir string) error {
	var continuationToken *string = nil

	// Update s3Prefix to include the deployID
	updatedS3Prefix := filepath.Join(s3Prefix, deployID)
	if publishDir != "" {
		updatedS3Prefix = filepath.Join(updatedS3Prefix, publishDir)
	}

	for {
		listInput := &s3.ListObjectsV2Input{
			Bucket:            aws.String(bucketName),
			Prefix:            aws.String(updatedS3Prefix),
			ContinuationToken: continuationToken,
		}

		result, err := client.ListObjectsV2(listInput)
		if err != nil {
			return fmt.Errorf("failed to list objects: %w", err)
		}

		if len(result.Contents) == 0 {
			break
		}

		for _, object := range result.Contents {
			relPath := strings.TrimPrefix(*object.Key, filepath.Join(s3Prefix, deployID))
			relPath = strings.TrimPrefix(relPath, string(filepath.Separator))
			localFilePath := filepath.Join(localDir, relPath)

			// Skip objects that represent directories
			if strings.HasSuffix(*object.Key, "/") {
				if err := os.MkdirAll(localFilePath, os.ModePerm); err != nil {
					return fmt.Errorf("failed to create directory %s: %w", localFilePath, err)
				}
				continue
			}

			// Ensure the parent directory exists
			if err := os.MkdirAll(filepath.Dir(localFilePath), os.ModePerm); err != nil {
				return fmt.Errorf("failed to create parent directory for %s: %w", localFilePath, err)
			}

			file, err := os.Create(localFilePath)
			if err != nil {
				return fmt.Errorf("failed to create file %s: %w", localFilePath, err)
			}
			defer file.Close()

			o, err := client.GetObject(&s3.GetObjectInput{
				Bucket: aws.String(bucketName),
				Key:    object.Key,
			})
			if err != nil {
				return fmt.Errorf("failed to get object %s: %w", *object.Key, err)
			}
			defer o.Body.Close()

			_, err = io.Copy(file, o.Body)
			if err != nil {
				return fmt.Errorf("failed to write object to file %s: %w", localFilePath, err)
			}

			log.Infof("下载文件: %s 到本地: %s\n", *object.Key, localFilePath)
		}

		// Check if there are more objects to fetch
		if !*result.IsTruncated {
			break
		}
		continuationToken = result.NextContinuationToken
	}

	return nil
}
