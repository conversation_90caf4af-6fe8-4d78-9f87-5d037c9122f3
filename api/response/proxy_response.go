package response

import (
	"bytes"
	"encoding/base64"
	"encoding/xml"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go/private/protocol"
	"github.com/aws/aws-sdk-go/private/protocol/xml/xmlutil"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// s3 cli

const (
	ServerInfo        = "Server"
	DefaultServerInfo = "BTFS"
	AcceptRanges      = "Accept-Ranges"
	AmzRequestID      = "x-amz-request-id"
)

const (
	noPayload   = "nopayload"
	mimeTypeXml = "application/xml"
)

func getRequestID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

type Error struct {
	code           string
	description    string
	httpStatusCode int
}

type errorOutput struct {
	_         struct{} `type:"structure"`
	Code      string   `locationName:"Code"`
	Message   string   `locationName:"Message"`
	Resource  string   `locationName:"Resource"`
	RequestID string   `locationName:"RequestID"`
}

func setCommonHeaders(header *http.Header) {
	header.Set(ServerInfo, DefaultServerInfo)
	header.Set(AcceptRanges, "bytes")
	header.Set(AmzRequestID, getRequestID())
}

func ProxyError(ctx *gin.Context, error *Error) {
	header := http.Header{}
	setCommonHeaders(&header)
	output := errorOutput{
		Code:      error.code,
		Message:   error.description,
		Resource:  ctx.Request.URL.Path,
		RequestID: "",
	}

	outv := reflect.Indirect(reflect.ValueOf(wrapOutput(output, "Error")))
	if !outv.IsValid() {
		ctx.Status(error.httpStatusCode)
		return
	}

	header.Set(AmzRequestID, ctx.Request.Header.Get(AmzRequestID))

	body, clen, ctyp, err := extractBody(outv)
	if err != nil {
		return
	}

	if body == nil {
		err = setLocationHeaders(header, outv)
		if err != nil {
			return
		}
		ctx.Status(error.httpStatusCode)
		return
	}

	defer body.Close()

	header.Set("Content-Length", fmt.Sprintf("%d", clen))
	header.Set("Content-Type", ctyp)

	err = setLocationHeaders(header, outv)
	if err != nil {
		return
	}

	ctx.Status(error.httpStatusCode)

	b, err := io.ReadAll(body)
	if err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if _, err = ctx.Writer.Write(b); err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	return
}

func Proxy(ctx *gin.Context, resp *http.Response) {
	// 设置响应头
	if resp == nil {
		return
	}

	for key, value := range resp.Header {
		for _, v := range value {
			ctx.Writer.Header().Add(key, v)
		}
	}

	// 设置响应状态码
	ctx.Status(resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if _, err := ctx.Writer.Write(body); err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
}

func wrapOutput(output interface{}, locationName string) (wrapper interface{}) {
	if locationName == "" {
		wrapper = output
		return
	}

	outputTag := fmt.Sprintf(`locationName:"%s" type:"structure"`, locationName)
	fields := []reflect.StructField{
		{
			Name:    "_",
			Type:    reflect.TypeOf(struct{}{}),
			Tag:     `payload:"Output" type:"structure"`,
			PkgPath: "responses",
		},
		{
			Name: "Output",
			Type: reflect.TypeOf(output),
			Tag:  reflect.StructTag(outputTag),
		},
	}
	wrtyp := reflect.StructOf(fields)
	wrval := reflect.New(wrtyp)
	wrval.Elem().FieldByName("Output").Set(reflect.ValueOf(output))
	wrapper = wrval.Interface()
	return
}
func setLocationHeaders(header http.Header, v reflect.Value) (err error) {
	for i := 0; i < v.NumField(); i++ {
		fv := reflect.Indirect(v.Field(i))
		ft := v.Type().Field(i)

		if n := ft.Name; n[0:1] == strings.ToLower(n[0:1]) {
			continue
		}

		if !fv.IsValid() {
			continue
		}

		if fv.Kind() == reflect.Interface && !fv.Elem().IsValid() {
			continue
		}

		switch ft.Tag.Get("location") {
		case "header":
			name := coalesceStr(ft.Tag.Get("locationName"), ft.Name)
			err = setHeaders(&header, fv, name, ft.Tag)
		case "headers":
			err = setHeadersMap(&header, fv, ft.Tag)
		}

		if err != nil {
			return
		}
	}

	return
}

var errValueNotSet = fmt.Errorf("value not set")

func setHeadersMap(header *http.Header, v reflect.Value, tag reflect.StructTag) (err error) {
	prefix := tag.Get("locationName")
	for _, key := range v.MapKeys() {
		var str string
		str, err = convertType(v.MapIndex(key), tag)
		if errors.Is(err, errValueNotSet) {
			err = nil
			continue
		}
		if err != nil {
			return
		}
		keyStr := strings.TrimSpace(key.String())
		str = strings.TrimSpace(str)
		header.Add(prefix+keyStr, str)
	}
	return
}

func coalesceStr(list ...string) string {
	for _, str := range list {
		if str != "" {
			return str
		}
	}
	return ""
}

func setHeaders(header *http.Header, v reflect.Value, name string, tag reflect.StructTag) (err error) {
	str, err := convertType(v, tag)
	if err != nil {
		return
	}
	name = strings.TrimSpace(name)
	str = strings.TrimSpace(str)
	header.Add(name, str)
	return
}

func convertType(v reflect.Value, tag reflect.StructTag) (str string, err error) {
	v = reflect.Indirect(v)
	if !v.IsValid() {
		return
	}

	switch value := v.Interface().(type) {
	case string:
		str = value
	case []byte:
		str = base64.StdEncoding.EncodeToString(value)
	case bool:
		str = strconv.FormatBool(value)
	case int64:
		str = strconv.FormatInt(value, 10)
	case time.Time:
		str = protocol.FormatTime(getTimeFormat(tag), value)
	default:
		err = fmt.Errorf("unsupported value type <%s>", v.Type())
	}

	return
}

func getTimeFormat(tag reflect.StructTag) string {
	format := tag.Get("timestampFormat")
	if len(format) == 0 {
		format = protocol.RFC822TimeFormatName
		if tag.Get("location") == "querystring" {
			format = protocol.ISO8601TimeFormatName
		}
	}
	return format
}

func extractBody(v reflect.Value) (body io.ReadCloser, clen int, ctyp string, err error) {
	ptyp, pfvl := getPayload(v)
	if ptyp == noPayload {
		return
	}

	if ptyp == "structure" || ptyp == "" {
		var buf bytes.Buffer
		buf.WriteString(xml.Header)
		err = xmlutil.BuildXML(v.Interface(), xml.NewEncoder(&buf))
		if err != nil {
			return
		}
		body = io.NopCloser(&buf)
		clen = buf.Len()
		ctyp = mimeTypeXml
		return
	}

	if pfvl.Interface() == nil {
		return
	}

	clen = -1

	body, ok := pfvl.Interface().(io.ReadCloser)
	if !ok {
		err = fmt.Errorf("unsupported payload type <%s>", pfvl.Type())
	}

	return
}

func getPayload(v reflect.Value) (ptyp string, pfvl reflect.Value) {
	ptyp = noPayload

	field, ok := v.Type().FieldByName("_")
	if !ok {
		return
	}

	noPayloadValue := field.Tag.Get(noPayload)
	if noPayloadValue != "" {
		return
	}

	payloadName := field.Tag.Get("payload")
	if payloadName == "" {
		return
	}

	pfld, ok := v.Type().FieldByName(payloadName)
	if !ok {
		return
	}

	ptyp = pfld.Tag.Get("type")
	pfvl = reflect.Indirect(v.FieldByName(payloadName))

	return
}
