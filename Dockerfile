##
## Build
##
FROM golang:1.22.7-alpine3.19  AS builder
RUN apk add build-base git
ARG GITLAB_TOKEN
RUN git config --global url."https://gitlab-ci:$<EMAIL>/".insteadOf "https://gitlab.insidebt.net/"

WORKDIR /app


COPY . .

ENV CGO_ENABLED=1
RUN go build -v -o storage3 -tags musl ./cmd

##
## Deploy
##
FROM alpine:3.20.3
RUN apk --update add ca-certificates

WORKDIR /app
COPY --from=builder /app/storage3  storage3

ENTRYPOINT ["./storage3"]
CMD ["run_api"]
