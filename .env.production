ACCESS=external

MIN_PODS=1
MAX_PODS=1
MIN_CPU=100m
MAX_CPU=200m
MIN_MEM=100Mi
MAX_MEM=200Mi

MIN_PODS_JOB=3
MAX_PODS_JOB=3
MIN_CPU_JOB=200m
MAX_CPU_JOB=2000m
MIN_MEM_JOB=200Mi
MAX_MEM_JOB=4000Mi

LOG_LEVEL="info"

DB_RW_USER="postgres"
DB_RW_PASSWORD="$POSTGRES_PWD_PROD"
DB_RW_HOST="$POSTGRES_HOST_PROD"
DB_RW_PORT="5432"
DB_RW_NAME="db_storage3"
DB_RW_MAX_OPEN_CONNS="32"
DB_RW_MAX_CONN_LIFE_TIME="1h"
DB_RW_MAX_CONN_IDLE_TIME="30m"

DB_RO_USER="postgres"
DB_RO_PASSWORD="$POSTGRES_PWD_PROD"
DB_RO_HOST="$POSTGRES_HOST_PROD"
DB_RO_PORT="5432"
DB_RO_NAME="db_storage3"
DB_RO_MAX_OPEN_CONNS="32"
DB_RO_MAX_CONN_LIFE_TIME="1h"
DB_RO_MAX_CONN_IDLE_TIME="30m"

LOG_DB_NAME="db_btfs_gateway_log"

REDIS_HOST="$REDIS_HOST_PROD"
REDIS_PASSWORD=""
REDIS_NETWORK="tcp"
REDIS_DB="0"
REDIS_MAX_IDLE_CONN="32"
REDIS_MAX_ACTIVE_CONN="32"
REDIS_CONN_IDLE_TIMEOUT="1m"
REDIS_CONN_WAIT="true"
REDIS_MAX_LIFE_TIME="1m"

SERVICE_PORT=5000

#BTFS_HOST="http://api.btfs-cluster:5001"
BTFS_HOST="http://btfs-nfthub-19b6e88ea5672e20.elb.ap-southeast-1.amazonaws.com:5001"

BTFS_UPLOAD_API_PATH="/api/v1/add"
HOST_IPS="http://*************:5001,http://*************:5001"
#S3_HOST="http://api.btfs-cluster:6001"
S3_HOST="http://btfs-nfthub-19b6e88ea5672e20.elb.ap-southeast-1.amazonaws.com:6001"

S3_HOST_IPS="http://*************:6001,http://*************:6001"
VISIT_TYPE="ip"

DNS_ROOT_DOMAIN="on.btfs.io"
DNS_ZONE_ID="Z081764031P6LI0A6Z3D9"

GITHUB_APP_ID="$GITHUB_APP_ID"
GITHUB_CLIENT_ID="$GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="$GITHUB_CLIENT_SECRET"
GITHUB_PRIVATE_KEY_PATH=""
GITHUB_ENDPOINT="https://github.com/login/oauth/access_token"

KAFKA_BOOTSTRAP_SERVERS="$KAFKA_PROD"

DEPLOY_JOB_TOPIC="t-storage3-deploy"
DEPLOY_JOB_GROUP_ID="g-storage3-deploy-v1.0"
DEPLOY_JOB_OFFSET_RESET="earliest"
DEPLOY_JOB_ROUTINES="2"

SITE_NAME_RESERVED_WORDS="sun,tron,justin,sunyuchen,justinsun,sunjustin,tronscan,justlend,apenft,btfs,trongrid,winklink,juststable,btt,trx,usdd,usdj,usdt,usdc,tusd,jst,wallet,scan,sunswap,suncurve,www,stusdt"
SITE_NAME_RESERVED_WORDS_ALLOWED_USER_ADDRESSES="******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,TV6pAYXCdVYcbW3Vtm8cnRv6ErPjjPcYym:TRON,TJuQa3zWp13AFQc9UbqzuqNMPh6ie3BXcR:TRON"
USER_ADDRESSES_WHITELIST="******************************************:TRON,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:TRON,******************************************:BTTC,******************************************:TRON,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,******************************************:BTTC,0x26b239e31fdac33142188b2aa77faf4642859dea:TRON"

TRON_API_HOST="https://api.trongrid.io"
TRON_API_KEY="8c5dcd5f-83d7-47b3-8446-745c01446308"

PROFITS_SUPER_USER_ADDRESSES="******************************************:BTTC,TDVpBiWtqDXk6xPyPavtRACF2krh3zxJEm:TRON"
PROFITS_FREE_STORE_SIZE="5GB"
PROFITS_GOLD_NFT_HOLDER_STORE_SIZE="150GB"
PROFITS_SILVER_NFT_HOLDER_STORE_SIZE="100GB"
PROFITS_BRONZE_NFT_HOLDER_STORE_SIZE="50GB"
PROFITS_FREE_SITE_SIZE="500MB"
PROFITS_GOLD_HOLDER_SITE_SIZE="1GB"
PROFITS_SILVER_NFT_HOLDER_SITE_SIZE="800MB"
PROFITS_BRONZE_NFT_HOLDER_SITE_SIZE="600MB"
PROFITS_FREE_SITE_NUM="600"
PROFITS_GOLD_NFT_HOLDER_SITE_NUM="200"
PROFITS_SILVER_NFT_HOLDER_SITE_NUM="150"
PROFITS_BRONZE_NFT_HOLDER_SITE_NUM="100"

AWS_OUTPUT_BUCKET="s3://btfs-logs/storage3/"
AWS_REGION="ap-southeast-1"

DOCKER_HOST="tcp://************:2375"
AWS_S3_BUCKET=for-docker-singapore
AWS_S3_ACCESS_ID=********************
AWS_S3_SECRET_ACCESS_KEY=Cuu3SsNiId7z2GfzaS8VdxWHqQ9g6Iibo91CX73a
AWS_S3_REGION=ap-southeast-1
