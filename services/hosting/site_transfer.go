package hosting

import (
	"errors"
	"fmt"
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/redis"
)

const TransferSiteDuration = 24 * 60 * 60

const (
	TransferSiteOperationAccept  = "ACCEPT"
	TransferSiteOperationDecline = "DECLINE"
)

func (s *service) TransferSite(user *model.User, arg *model.TransferSiteArg) error {
	// 确保用户拥有该站点
	site, err := data.QuerySiteRecord(user.UserId, arg.SiteId)
	if err != nil {
		return err
	}

	err = ValidateTransferSiteSignature(arg, site, user)
	if err != nil {
		return err
	}

	// 确保站点未被删除
	if site.IsDeleted == model.SiteDeleteYes {
		err = ErrSiteDeleted
		return err
	}

	err = freezeSite(arg.SiteId)
	if err != nil {
		return err
	}

	err = data.InsertSiteTransferRecord(&model.SiteTransferRecord{
		SiteId:               arg.SiteId,
		FromUserId:           user.UserId,
		FromAddress:          user.Address,
		FromAddressType:      user.Source,
		FromSignature:        arg.Signature,
		ToAddress:            arg.TargetAddress,
		TransferStatus:       model.TransferStatusPending,
		ToAddressType:        model.UserSource(arg.TargetAddressType),
		FromSignatureMessage: arg.SignatureMessage,
		CreatedAt:            arg.TransferCreatedAt,
	})

	if err != nil {
		_ = unFreezeSite(arg.SiteId)
		return err
	}

	// 开启一个定时器，超时没有接收或者拒绝就恢复，如果被接收了或者被拒绝了就不需要了
	go func() {
		defer func() {
			err := recover()
			if err != nil {
				fmt.Println("ticker delete transfer site err = ", err)
			}

		}()

		// 用掉的时间
		usedSeconds := time.Now().UTC().Sub(arg.TransferCreatedAt).Seconds()
		remain := time.Duration(TransferSiteDuration - usedSeconds)
		<-time.After(remain * time.Second)
		err = data.ExpireTransferSiteRecord(arg.SiteId, arg.TargetAddress)
		fmt.Println("ticker delete transfer site executed ok, the result is: ", err)
		_ = unFreezeSite(arg.SiteId)
	}()

	return nil
}

func (s *service) AcceptOrDeclineSite(user *model.User, arg *model.TransferSiteOpArg) error {
	// get pending transfer site
	ts, err := data.GetSitePendingTransferRecord(arg.SiteId, user.Address)
	if err != nil {
		return err
	}
	if ts == nil {
		return errors.New("the site doesn't exists")
	}

	m := &model.SiteTransferRecord{
		SiteId:             arg.SiteId,
		ToAddress:          user.Address,
		ToSignature:        arg.Signature,
		ToSignatureMessage: arg.SignatureMessage,
		ToUserId:           user.UserId,
	}

	if arg.Operation == TransferSiteOperationAccept {
		m.TransferStatus = model.TransferStatusAccepted
	} else {
		m.TransferStatus = model.TransferStatusDeclined
	}

	site, err := data.GetTransferSiteRecord(arg.SiteId, ts.FromUserId)
	if err != nil {
		return err
	}
	if site == nil {
		return errors.New("site doesn't exists")
	}

	if arg.Operation == TransferSiteOperationAccept {
		err = ValidateAcceptTransferSiteSignature(arg, ts, site)
		if err != nil {
			return err
		}
	}

	err = data.UpdateSiteTransferRecordStatus(m)
	if err != nil {
		return err
	}

	if arg.Operation == TransferSiteOperationDecline {
		// err = data.UnDeleteTransferSiteRecord(arg.SiteId, ts.FromAddress)
		// if err != nil {
		// 	return err
		// }
		return unFreezeSite(arg.SiteId)
	}

	if arg.Operation == TransferSiteOperationAccept {
		isDeleted, err := data.DeleteSiteRecordWithSiteId(arg.SiteId)
		if err != nil {
			return err
		}
		if !isDeleted {
			return errors.New("can't delete site, it doesn't exists")
		}
	}

	site.UserId = user.UserId
	site.IsDeleted = model.SiteDeletedNot
	err = data.InsertSiteRecord(site)
	if err != nil {
		return err
	}

	return unFreezeSite(arg.SiteId)
}

const LockSitePrefix = "lock_site_%s"

func freezeSite(siteId string) error {
	key := fmt.Sprintf(LockSitePrefix, siteId)
	_, err := redis.Lock(key, 0, TransferSiteDuration)
	if err != nil {
		if errors.Is(err, redis.ErrLockBusy) {
			err = ErrSiteBusy
		}
		return err
	}
	return nil
}

func unFreezeSite(siteId string) error {
	_, err := redis.Del(fmt.Sprintf(LockSitePrefix, siteId))
	return err
}

func (s *service) GetTransferringSite(user *model.User) (sites []*model.SiteTransferRecord, err error) {
	return data.GetTransferringSite(user.UserId)
}

func (s *service) GetSiteTransferHistory(siteId string) (sites []*model.SiteTransferRecord, err error) {
	return data.GetSiteTransferHistory(siteId)
}

func (s *service) GetTransferPendingSite(user *model.User) (sites []*model.SiteTransferRecord, err error) {
	return data.GetPendingSiteByUserId(user.Address, user.UserId)
}

func (s *service) GetTransferringSiteBySiteId(siteId string, user *model.User, isLatest bool) (site *model.SiteTransferRecord, err error) {
	return data.GetTransferringSiteBySiteId(siteId, user.Address, user.UserId, isLatest)
}
