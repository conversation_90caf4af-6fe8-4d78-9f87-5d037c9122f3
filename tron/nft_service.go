package tron

import "context"

type NFTService interface {
	GetNFTTransferEvents(ctx context.Context, arg *GetNFTTransferEventsArg) (rsp *GetNFTTransferEventsResponseBody, err error)
	GetNFTTokenUri(ctx context.Context, arg *GetNFTTokenUriArg) (rsp *GetNFTTokenUriResponseBody, err error)
}

type GetNFTTransferEventsArg struct {
	ContractAddress   string `json:"contract_address"`
	MinBlockTimestamp int64  `json:"min_block_timestamp"`
	Fingerprint       string `json:"fingerprint"`
	Limit             int32  `json:"limit"` // Number of transactions per page, default 20, max 200
}

type NFTTransferEventItem struct {
	BlockNumber    int64  `json:"block_number"`
	BlockTimestamp int64  `json:"block_timestamp"`
	TokenId        string `json:"token_id"`
	From           string `json:"from"`
	To             string `json:"to"`
}

type GetNFTTransferEventsResponseBody struct {
	Events      []*NFTTransferEventItem `json:"events"`
	FingerPrint string                  `json:"finger_print"`
}

type GetNFTTokenUriArg struct {
	ContractAddress string `json:"contract_address"`
	TokenId         string `json:"token_id"`
}

type GetNFTTokenUriResponseBody struct {
	TokenUri string `json:"token_uri"`
}
