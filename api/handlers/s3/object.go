package s3

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	biz "gitlab.insidebt.net/btfs/storage3-backend/btfs/s3"
	"gitlab.insidebt.net/btfs/storage3-backend/services/s3"
	"net/http"
	"net/url"
)

// object

// PutObject
// aws s3api put-object --bucket bucket0 --key test-put/a.txt --body test-put/a.txt --endpoint-url http://127.0.0.1:1501
// aws s3api put-object --bucket bucket0 --key folder1/  --endpoint-url http://127.0.0.1:1501  FAILURE btfs not support
// CopyObject aws s3api copy-object --copy-source mybucket10/main.go  --bucket mybucket3 --key main.go --endpoint-url http://127.0.0.1:6001
// upload multipart : aws s3api upload-part --bucket mybucket10 --key send_error.go --upload-id eff57dfb-ffb5-48f4-87fd-b4e673b367b7 --part-number 1 --body send_error.go --endpoint-url http://127.0.0.1:6001
func PutObject(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	bucketName := ctx.Param(BucketPath)
	objectName := ctx.Param(ObjectPath) + ctx.Param(SubPath)
	objectName, err = url.PathUnescape(objectName) // upload folder should be decoded
	bizInfo := &biz.BizInfo{BucketName: bucketName, ObjectName: objectName}

	if ctx.Request.Header.Get("X-Amz-Copy-Source") != "" {
		var resp *http.Response
		resp, err = s3.CopyObject(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, resp)
		return
	}

	if ctx.Request.URL.Query().Has("uploadId") {
		resp, err := s3.UploadMultiPart(bizInfo, ctx.Request)

		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, resp)
		return
	}

	resp, err := s3.AddObject(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}

	response.Proxy(ctx, resp)
}

// HeadObject aws s3api head-object --bucket mybucket10 --key daemon.go --endpoint-url http://127.0.0.1:1501
func HeadObject(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	bucketName := ctx.Param(BucketPath)
	objectName := ctx.Param(ObjectPath) + ctx.Param(SubPath)
	obj, err := s3.GetObject(&biz.BizInfo{BucketName: bucketName, ObjectName: objectName}, ctx.Request)

	if err != nil {
		errorProcess(ctx, err)
		return
	}

	response.Proxy(ctx, obj)

}

// ListObject
// GetBucketACL       aws s3api get-bucket-acl --bucket mybucket10 --endpoint-url http://127.0.0.1:1501
// ListBucketObject   aws s3api list-objects --bucket mybucket10 --endpoint-url http://127.0.0.1:6001  # listBucket
// ListBucketObjectV2 aws s3api list-objects-v2 --bucket bucket0 --endpoint-url http://127.0.0.1:1501
func ListObject(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	bucketName := ctx.Param(BucketPath)

	bizInfo := &biz.BizInfo{BucketName: bucketName}

	params := ctx.Request.URL.Query()

	if params.Has("acl") {
		var bucket *http.Response
		bucket, err = s3.GetBucketACL(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, bucket)
		return
	}

	if params.Has("list-type") {
		var objs *http.Response
		objs, err = s3.ListObject(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, objs)
		return
	}

	// list bucket
	objs, err := s3.ListObject(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}
	response.Proxy(ctx, objs)
}

// DeleteObject aws s3api delete-object --bucket mybucket10 --key daemon.go --endpoint-url http://127.0.0.1:6001
// abort multipart upload: aws s3api abort-multipart-upload --bucket mybucket10 --key send_error.go --upload-id 31c854c4-31fc-4322-9e47-5437a7611232 --endpoint-url http://127.0.0.1:6001
func DeleteObject(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	params := ctx.Request.URL.Query()
	bucketName := ctx.Param(BucketPath)
	objectName := ctx.Param(ObjectPath) + ctx.Param(SubPath)
	objectName, err = url.PathUnescape(objectName) // upload folder should be decoded

	bizInfo := &biz.BizInfo{
		BucketName: bucketName,
		ObjectName: objectName,
	}

	if params.Has("uploadId") {
		var resp *http.Response
		resp, err = s3.AbortMultiPartUpload(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, resp)
	}

	resp, err := s3.DeleteObject(bizInfo, ctx.Request)
	response.Proxy(ctx, resp)

}

func DeleteObjects(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	bucketName := ctx.Param(BucketPath)
	objectName := ctx.Param(ObjectPath) + ctx.Param(SubPath)

	objectName, err = url.PathUnescape(objectName) // upload folder should be decoded

	bizInfo := &biz.BizInfo{
		BucketName: bucketName,
		ObjectName: objectName,
	}

	resp, err := s3.DeleteObject(bizInfo, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}
	response.Proxy(ctx, resp)
}

// GetObjectAcl aws s3api get-object-acl --bucket mybucket10 --key main.go --endpoint-url http://127.0.0.1:6001
func GetObjectAcl(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	obj, err := s3.GetObject(&biz.BizInfo{BucketName: ctx.Param(BucketPath), ObjectName: ctx.Param(ObjectPath) + ctx.Param(SubPath)}, ctx.Request)
	if err != nil {
		errorProcess(ctx, err)
		return
	}
	response.Proxy(ctx, obj)
}

// MultiPartUpload
// create multipart upload : aws s3api create-multipart-upload --bucket mybucket10 --key send_error.go --endpoint-url http://127.0.0.1:6001
// complete multipart upload : aws s3api complete-multipart-upload --bucket mybucket10 --key send_error.go --upload-id eff57dfb-ffb5-48f4-87fd-b4e673b367b7 --endpoint-url http://127.0.0.1:6001
func MultiPartUpload(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	params := ctx.Request.URL.Query()
	bucketName := ctx.Param(BucketPath)
	objectName := ctx.Param(ObjectPath) + ctx.Param(SubPath)
	objectName, err = url.PathUnescape(objectName) // upload folder should be decoded

	bizInfo := &biz.BizInfo{
		BucketName: bucketName,
		ObjectName: objectName,
	}

	if params.Has("uploads") {
		var resp *http.Response
		resp, err = s3.CreateMultiPartUpload(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, resp)
		return
	}

	if params.Has("uploadId") {
		var resp *http.Response
		resp, err = s3.CompleteMultiPartUpload(bizInfo, ctx.Request)
		if err != nil {
			errorProcess(ctx, err)
			return
		}
		response.Proxy(ctx, resp)
	}
}

func DownloadObject(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	_, err = cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	cid := ctx.Request.URL.Query().Get("arg")
	resp, err := s3.DownloadObject(ctx.Request.Context(), cid)
	if err != nil {
		errorProcess(ctx, err)
		return
	}

	response.Proxy(ctx, resp)
}
