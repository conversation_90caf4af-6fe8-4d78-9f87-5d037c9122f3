package postgres

import (
	"context"
	"errors"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"reflect"
)

type Batch struct {
	obatch *pgx.Batch
}

func NewBatch() *Batch {
	return &Batch{
		obatch: &pgx.Batch{},
	}
}

func (batch *Batch) Queue(query string, args ...interface{}) {
	batch.obatch.Queue(query, args...)
}

func (batch *Batch) Len() int {
	return batch.obatch.Len()
}

func (batch *Batch) GetByRW(dstPtr interface{}) (err error) {
	return batch.get(rwPool, dstPtr)
}

func (batch *Batch) GetByRO(dstPtr interface{}) (err error) {
	return batch.get(roPool, dstPtr)
}

func (batch *Batch) get(pool *pgxpool.Pool, dstsPtr interface{}) (err error) {
	err = batch.checkGetDst(dstsPtr)
	if err != nil {
		return
	}
	n := batch.Len()
	dstsRt := reflect.TypeOf(dstsPtr).Elem()
	dstsRv := reflect.MakeSlice(dstsRt, n, n)
	br := pool.SendBatch(context.Background(), batch.obatch)
	defer br.Close()
	for i := 0; i < n; i++ {
		var rows pgx.Rows
		rows, err = br.Query()
		if err != nil {
			return
		}
		dstPtrRt := dstsRt.Elem()
		dstPtrRv := reflect.New(dstPtrRt.Elem())
		err = scan.ScanOne(dstPtrRv.Interface(), rows)
		notFound := errors.Is(err, pgx.ErrNoRows)
		if err != nil && !notFound {
			return
		}
		if notFound {
			dstPtrRv = reflect.New(dstPtrRt).Elem()
			err = nil
		}
		dstsRv.Index(i).Set(dstPtrRv)
	}
	reflect.ValueOf(dstsPtr).
		Elem().
		Set(dstsRv)
	return
}

func (batch *Batch) checkGetDst(dstsPtr interface{}) (err error) {
	dstsPtrRv := reflect.ValueOf(dstsPtr)
	if dstsPtrRv.Kind() != reflect.Ptr || dstsPtrRv.IsNil() {
		err = errors.New("must be no-nil ptr")
		return
	}
	dstsRv := dstsPtrRv.Elem()
	if dstsRv.Kind() != reflect.Slice {
		err = errors.New("must be slice ptr")
		return
	}
	itemPtrRt := dstsRv.Type().Elem()
	if itemPtrRt.Kind() != reflect.Ptr {
		err = errors.New("item must be ptr")
		return
	}
	return
}
