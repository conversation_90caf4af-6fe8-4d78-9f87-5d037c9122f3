package docker

import (
	"context"
)

var svc Service

func init() {
	var err error
	svc, err = newService()
	if err != nil {
		panic(err)
	}
}

func ContainerRunAndClean(ctx context.Context, image string, workDir string, command []string, mounts []Mount, handleLog func(line string)) (statusCode int64, err error) {
	return svc.ContainerRunAndClean(ctx, image, workDir, command, mounts, handleLog)
}

func MountVolumeToS3(path, deployID string) error {
	return svc.MountVolume(path, deployID)
}

func UnmountVolumeFromS3(remotePath, deployID string) error {
	return svc.UnmountVolume(remotePath, deployID)
}

func CopyFilesFromS3(path, deployID, publishDir string) error {
	return svc.CopyVolume(path, deployID, publishDir)
}
