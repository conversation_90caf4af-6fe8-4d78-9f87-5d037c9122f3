package btfs

import (
	"context"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var svc Service

func init() {
	svc = newService(config.BTFS.Host, config.BTFS.UploadApiPath, config.BTFS.RemoveApiPath)
}

func UploadItem(ctx context.Context, item string, options *UploadOptions) (result *UploadItemResult, err error) {
	return svc.UploadItem(ctx, item, options)
}

func UploadDir(ctx context.Context, dir string, options *UploadOptions) (results []*UploadItemResult, err error) {
	return svc.UploadDir(ctx, dir, options)
}

func Remove(options *RemoveOptions) (result *RemoveResult, err error) {
	return svc.Remove(options)
}
