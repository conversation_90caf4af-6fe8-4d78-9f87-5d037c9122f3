package s3

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"unicode/utf8"
)

var (
	SignV4Algorithm = "AWS4-HMAC-SHA256"
	Separator       = "/"
)

type Credential struct {
	AccessKey string
	Date      string
	Region    string
	Service   string
	Request   string
}

type Authorization struct {
	Prefix        string
	Credential    *Credential
	SignedHeaders []string
	Signature     string
}

var (
	SignError = errors.New("sign error")
)

func ValidateSignature(request *http.Request, secret string) error {
	authorization := request.Header.Get("Authorization")
	auth, err := unMarshall(authorization)
	if err != nil {
		return err
	}
	checkSig, err := getAuthorization(request, secret)
	if err != nil {
		return err
	}
	if !checkSignature(auth.Signature, checkSig.Signature) {
		return SignError
	}
	return nil
}

func GenerationAuthorization(request *http.Request, secret string) (string, error) {
	auth, err := getAuthorization(request, secret)
	if err != nil {
		return "", err
	}
	return marshall(auth), nil
}

func getAuthorization(request *http.Request, secret string) (*Authorization, error) {
	auth, err := unMarshall(request.Header.Get("Authorization"))
	if err != nil {
		return nil, err
	}

	signKey := getSigningKey(secret, auth.Credential.Date, auth.Credential.Region)
	signValue, err := getSignValue(request)
	if err != nil {
		return nil, err
	}

	hash := hmac.New(sha256.New, signKey)
	hash.Write([]byte(signValue))
	sig := hex.EncodeToString(hash.Sum(nil))
	auth.Signature = sig
	return auth, nil
}

func getSigningKey(secretKey string, t string, scope string) []byte {
	date := sumHMAC([]byte("AWS4"+secretKey), []byte(t))
	regionBytes := sumHMAC(date, []byte(scope))
	service := sumHMAC(regionBytes, []byte("s3"))
	signingKey := sumHMAC(service, []byte("aws4_request"))
	return signingKey
}

func getSignValue(request *http.Request) (string, error) {
	auth, err := unMarshall(request.Header.Get("Authorization"))
	if err != nil {
		return "", err
	}

	stringToSign := SignV4Algorithm + "\n" + request.Header.Get("X-Amz-Date") + "\n"

	stringToSign += strings.Join([]string{
		auth.Credential.Date,
		auth.Credential.Region,
		auth.Credential.Service,
		auth.Credential.Request,
	}, Separator) + "\n"

	canonicalRequest := getCanonicalRequest(auth.SignedHeaders, request)

	canonicalRequestBytes := sha256.Sum256([]byte(canonicalRequest))

	stringToSign += hex.EncodeToString(canonicalRequestBytes[:])

	return stringToSign, nil
}

func checkSignature(signature string, check string) bool {
	return signature == check
}

func marshall(auth *Authorization) string {
	credential := strings.Join([]string{auth.Credential.AccessKey, auth.Credential.Date, auth.Credential.Region, auth.Credential.Service, auth.Credential.Request}, Separator)
	return fmt.Sprintf("%s Credential=%s, SignedHeaders=%s, Signature=%s", auth.Prefix, credential, strings.Join(auth.SignedHeaders, ";"), auth.Signature)
}

func unMarshall(authorization string) (*Authorization, error) {
	authField := strings.Split(strings.TrimSpace(strings.TrimPrefix(authorization, SignV4Algorithm)), ",")

	credentials := strings.Split(strings.TrimPrefix(authField[0], "Credential="), "/")

	signedHeaders := strings.Split(strings.TrimPrefix(strings.TrimSpace(authField[1]), "SignedHeaders="), ";")
	signature := strings.TrimPrefix(strings.TrimSpace(authField[2]), "Signature=")

	return &Authorization{
		Prefix:        SignV4Algorithm,
		Credential:    &Credential{AccessKey: credentials[0], Date: credentials[1], Region: credentials[2], Service: credentials[3], Request: credentials[4]},
		SignedHeaders: signedHeaders,
		Signature:     signature,
	}, nil

}

func getCanonicalRequest(signedHeaders []string, request *http.Request) string {
	header, _ := extractSignedHeaders(signedHeaders, request)
	rawQuery := strings.ReplaceAll(request.URL.Query().Encode(), "+", "%20")
	encodedPath := encodePath(request.URL.Path)
	canonicalRequest := strings.Join([]string{
		request.Method,
		encodedPath,
		rawQuery,
		getCanonicalHeaders(header),
		getSignedHeaders(header),
		request.Header.Get("X-Amz-Content-Sha256"),
	}, "\n")

	return canonicalRequest
}

func extractSignedHeaders(signedHeaders []string, r *http.Request) (http.Header, error) {
	reqHeaders := r.Header
	reqQueries := r.Form
	// find whether "host" is part of list of signed headers.
	// if not return ErrUnsignedHeaders. "host" is mandatory.
	if !contains(signedHeaders, "host") {
		return nil, errors.New("aaa")
	}
	extractedSignedHeaders := make(http.Header)
	for _, header := range signedHeaders {
		// `host` will not be found in the headers, can be found in r.Host.
		// but its alway necessary that the list of signed headers containing host in it.
		val, ok := reqHeaders[http.CanonicalHeaderKey(header)]
		if !ok {
			// try to set headers from Query String
			val, ok = reqQueries[header]
		}
		if ok {
			extractedSignedHeaders[http.CanonicalHeaderKey(header)] = val
			continue
		}
		switch header {
		case "expect":
			// Golang http server strips off 'Expect' header, if the
			// client sent this as part of signed headers we need to
			// handle otherwise we would see a signature mismatch.
			// `aws-cli` sets this as part of signed headers.
			//
			// According to
			// http://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.20
			// Expect header is always of form:
			//
			//   Expect       =  "Expect" ":" 1#expectation
			//   expectation  =  "100-continue" | expectation-extension
			//
			// So it safe to assume that '100-continue' is what would
			// be sent, for the time being keep this work around.
			// Adding a *TODO* to remove this later when Golang server
			// doesn't filter out the 'Expect' header.
			extractedSignedHeaders.Set(header, "100-continue")
		case "host":
			extractedSignedHeaders.Set(header, r.Host)
		case "transfer-encoding":
			// Go http server removes "host" from Request.Header
			extractedSignedHeaders[http.CanonicalHeaderKey(header)] = r.TransferEncoding
		case "content-length":
			// Signature-V4 spec excludes Content-Length from signed headers list for signature calculation.
			// But some clients deviate from this rule. Hence we consider Content-Length for signature
			// calculation to be compatible with such clients.
			extractedSignedHeaders.Set(header, strconv.FormatInt(r.ContentLength, 10))
		default:
			return nil, errors.New("ErrUnsignedHeaders")
		}
	}
	return extractedSignedHeaders, nil
}

func getSignedHeaders(signedHeaders http.Header) string {
	var headers []string
	for k := range signedHeaders {
		headers = append(headers, strings.ToLower(k))
	}
	sort.Strings(headers)
	return strings.Join(headers, ";")
}

func getCanonicalHeaders(signedHeaders http.Header) string {
	var headers []string
	vals := make(http.Header)
	for k, vv := range signedHeaders {
		headers = append(headers, strings.ToLower(k))
		vals[strings.ToLower(k)] = vv
	}
	sort.Strings(headers)

	var buf bytes.Buffer
	for _, k := range headers {
		buf.WriteString(k)
		buf.WriteByte(':')
		for idx, v := range vals[k] {
			if idx > 0 {
				buf.WriteByte(',')
			}
			buf.WriteString(signV4TrimAll(v))
		}
		buf.WriteByte('\n')
	}
	return buf.String()
}

func signV4TrimAll(input string) string {
	// Compress adjacent spaces (a space is determined by
	// unicode.IsSpace() internally here) to one space and return
	return strings.Join(strings.Fields(input), " ")
}

func sumHMAC(key []byte, data []byte) []byte {
	hash := hmac.New(sha256.New, key)
	hash.Write(data)
	return hash.Sum(nil)
}

var reservedObjectNames = regexp.MustCompile("^[a-zA-Z0-9-_.~/]+$")

func encodePath(pathName string) string {

	if reservedObjectNames.MatchString(pathName) {
		return pathName
	}
	var encodedPathname string
	for _, s := range pathName {
		if 'A' <= s && s <= 'Z' || 'a' <= s && s <= 'z' || '0' <= s && s <= '9' { // §2.3 Unreserved characters (mark)
			encodedPathname = encodedPathname + string(s)
			continue
		}
		switch s {
		case '-', '_', '.', '~', '/': // §2.3 Unreserved characters (mark)
			encodedPathname = encodedPathname + string(s)
			continue
		default:
			length := utf8.RuneLen(s)
			if length < 0 {
				// if utf8 cannot convert return the same string as is
				return pathName
			}
			u := make([]byte, length)
			utf8.EncodeRune(u, s)
			for _, r := range u {
				hexValue := hex.EncodeToString([]byte{r})
				encodedPathname = encodedPathname + "%" + strings.ToUpper(hexValue)
			}
		}
	}
	return encodedPathname
}

func contains(slice interface{}, elem interface{}) bool {
	v := reflect.ValueOf(slice)
	if v.Kind() == reflect.Slice {
		for i := 0; i < v.Len(); i++ {
			if v.Index(i).Interface() == elem {
				return true
			}
		}
	}
	return false
}
