package s3

const (
	PublicReadWrite = "public-read-write"
	PublicRead      = "public-read"
	Private         = "private"
)

const (
	// --- bucket

	// CreateBucketAction - CreateBucket Rest API action.
	CreateBucketAction = "s3:CreateBucket"

	// HeadBucketAction - HeadBucket Rest API action.
	HeadBucketAction = "s3:HeadBucket"

	// ListBucketAction - ListBucket Rest API action.
	ListBucketAction = "s3:ListBucket"

	// DeleteBucketAction - DeleteBucket Rest API action.
	DeleteBucketAction = "s3:DeleteBucket"

	// PutBucketAclAction - PutBucketACL Rest API action.
	PutBucketAclAction = "s3:PutBucketACL"

	// GetBucketAclAction - GetBucketACL Rest API action.
	GetBucketAclAction = "s3:GetBucketACL"

	// --- object

	// ListObjectsAction - ListObjects Rest API action.
	ListObjectsAction = "s3:ListObjects"

	// ListObjectsV2Action - ListObjectsV2 Rest API action.
	ListObjectsV2Action = "s3:ListObjectsV2"

	// HeadObjectAction - HeadObject Rest API action.
	HeadObjectAction = "s3:HeadObject"

	// PutObjectAction - PutObject Rest API action.
	PutObjectAction = "s3:PutObject"

	// GetObjectAction - GetObject Rest API action.
	GetObjectAction = "s3:GetObject"

	// CopyObjectAction - CopyObject Rest API action.
	CopyObjectAction = "s3:CopyObject"

	// DeleteObjectAction - DeleteObject Rest API action.
	DeleteObjectAction = "s3:DeleteObject"

	// DeleteObjectsAction - DeleteObjects Rest API action.
	DeleteObjectsAction = "s3:DeleteObjects"

	// --- multipart upload

	// CreateMultipartUploadAction - CreateMultipartUpload Rest API action.
	CreateMultipartUploadAction Action = "s3:CreateMultipartUpload"

	// AbortMultipartUploadAction - AbortMultipartUpload Rest API action.
	AbortMultipartUploadAction Action = "s3:AbortMultipartUpload"

	// CompleteMultipartUploadAction - CompleteMultipartUpload Rest API action.
	CompleteMultipartUploadAction Action = "s3:CompleteMultipartUpload"

	// UploadPartAction - UploadPartUpload Rest API action.
	UploadPartAction Action = "s3:UploadPartUpload"
)

var supportedBucketActions = map[Action]struct{}{
	CreateBucketAction: {},
	HeadBucketAction:   {},
	ListBucketAction:   {},
	DeleteBucketAction: {},
	PutBucketAclAction: {},
	GetBucketAclAction: {},
}

// List of all supported object actions.
var supportedObjectActions = map[Action]struct{}{
	ListObjectsAction:   {},
	ListObjectsV2Action: {},
	HeadObjectAction:    {},
	PutObjectAction:     {},
	GetObjectAction:     {},
	CopyObjectAction:    {},
	DeleteObjectAction:  {},
	DeleteObjectsAction: {},

	CreateMultipartUploadAction:   {},
	AbortMultipartUploadAction:    {},
	CompleteMultipartUploadAction: {},
	UploadPartAction:              {},
}

type Action string

func (action Action) IsBucketAction() bool {
	_, ok := supportedBucketActions[action]
	return ok
}

// IsObjectAction - returns whether action is object type or not.
func (action Action) IsObjectAction() bool {
	_, ok := supportedObjectActions[action]
	return ok
}

func checkACL(owner, acl, user string, act Action) (allow bool) {
	own := user != "" && user == owner
	allow = isAllowed(own, acl, act)
	return
}

func isAllowed(own bool, acl string, action Action) (allow bool) {
	if own {
		return true
	}

	if action.IsBucketAction() {
		return false
	}

	if action.IsObjectAction() {
		switch acl {
		case Private:
			return own
		case PublicRead:
			return checkActionInPublicRead(action)
		case PublicReadWrite:
			return checkActionInPublicReadWrite(action)
		}
	}

	return false
}

var rdActionMap = map[Action]struct{}{
	ListObjectsAction:   {},
	ListObjectsV2Action: {},
	HeadObjectAction:    {},
	GetObjectAction:     {},
}

var rwActionMap = map[Action]struct{}{
	ListObjectsAction:             {},
	ListObjectsV2Action:           {},
	HeadObjectAction:              {},
	PutObjectAction:               {},
	GetObjectAction:               {},
	CopyObjectAction:              {},
	DeleteObjectAction:            {},
	DeleteObjectsAction:           {},
	CreateMultipartUploadAction:   {},
	AbortMultipartUploadAction:    {},
	CompleteMultipartUploadAction: {},
	UploadPartAction:              {},
}

// checkActionInPublicRead - returns whether action is Read or not.
func checkActionInPublicRead(action Action) bool {
	_, ok := rdActionMap[action]
	return ok
}

// checkActionInPublicReadWrite - returns whether action is RW or not.
func checkActionInPublicReadWrite(action Action) bool {
	_, ok := rwActionMap[action]
	return ok
}
