package handlers

import (
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/services/profits"
)

func GetUserProfitsStat(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	stat, err := profits.GetUserProfitsStat(user)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	response.DataOK(ctx, response.Data{
		"stat": stat,
	})
}
