package utils

import (
	"testing"
)

func TestExtractBTTCAddressBySignature(t *testing.T) {
	type args struct {
		signatureData string
		signature     string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "should return ok",
			args: args{
				signature:     "0x709f470f739ed83c1e689815e225a601a252b613589705a69716afd1203b90de349575feba68c070dfae784f8cef9bdf97184b3a4c15b0c194c176c8141525131b",
				signatureData: `{"operation":"SiteOwnershipTransfer","siteName":"fit-ocean-3412.on.bttscan.net","siteID":"5135e37d-cd5b-420f-a138-df4f888833cf","from":"0x894d1419e0399fb843d2477d3f931d41b484de82","to":"0x532a5662d7a283a00e47dc043e89df238ca5ae5d","startTime":"2024-11-13 10:48:08(UTC)","dueTime":"2024-11-13 10:58:08(UTC)"}`,
			},
			want:    "0x894d1419e0399fb843d2477d3f931d41b484de82",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractBTTCAddressBySignature(tt.args.signatureData, tt.args.signature)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractBTTCAddressBySignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ExtractBTTCAddressBySignature() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExtractTronAddressBySignature(t *testing.T) {
	type args struct {
		signatureData string
		signature     string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "should return ok",
			args: args{
				signature:     "0xbb090ce71b5e175438ae345b8f370c00ad169c66e040e84cb53a57a1639a1fc557fd3c9d4cd6dd317a27d5e73fac11c85ed66991a788975f0e192dc0929e84cd1c",
				signatureData: `{"operation":"SiteOwnershipTransfer","siteName":"scenic-snakes-3775.on.bttscan.net","siteID":"113fa8d1-7d74-4d77-97e2-0f636048911b","from":"TK2PZfvt3UK4RmZuoBaJjfzBN55K8rDpnG","to":"TB4wSfLWK2UWENQ3RAV8enMhJAZmQyNm6m","startTime":"2024-11-13 13:12:22(UTC)","dueTime":"2024-11-13 13:22:22(UTC)"}`,
			},
			want:    "TK2PZfvt3UK4RmZuoBaJjfzBN55K8rDpnG", // 0x63533267e39a25a343eb2274b22511a36a76649b
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractTronAddressBySignature(tt.args.signatureData, tt.args.signature)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractTronAddressBySignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ExtractTronAddressBySignature() got = %v, want %v", got, tt.want)
			}
		})
	}
}
