package data

import (
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/postgres"
)

const GenesisNFTContractAddress = "TQfujfp9LNU4yRYcAiXcLRmjDuVKQ8U3AJ"

var genesisNFTTokenUriToMaterialMap = map[string]string{
	"https://gateway.btfs.io/btfs/QmVs42A8YqLffAC75kjQEMxRbzZDdQFKhRiM4agnwNX7u6/BTFS-GenesisNFT-Level-P": model.GenesisNFTMaterialGold,
	"https://gateway.btfs.io/btfs/QmVs42A8YqLffAC75kjQEMxRbzZDdQFKhRiM4agnwNX7u6/BTFS-GenesisNFT-Level-T": model.GenesisNFTMaterialSilver,
	"https://gateway.btfs.io/btfs/QmVs42A8YqLffAC75kjQEMxRbzZDdQFKhRiM4agnwNX7u6/BTFS-GenesisNFT-Level-G": model.GenesisNFTMaterialBronze,
}

func QueryGenesisNFTMaterialByUri(tokenUri string) (material string, ok bool) {
	material, ok = genesisNFTTokenUriToMaterialMap[tokenUri]
	return
}

const queryGenesisNFTLastBlockTimestampSql = `select coalesce(max(block_timestamp), 0) from tb_genesis_nft`

func QueryGenesisNFTLastBlockTimestamp() (blockTimestamp int64, err error) {
	err = postgres.GetByRW(queryGenesisNFTLastBlockTimestampSql, &blockTimestamp)
	return
}

const updateGenesisNFTSql = `update tb_genesis_nft 
	set owner_address = $1, block_number = $2, block_timestamp = $3, updated_at = (now() at time zone 'utc') 
where token_id = $4`

func UpdateGenesisNFT(arg *model.UpdateGenesisNFTArg) (updated bool, err error) {
	tag, err := postgres.ExecByRW(
		updateGenesisNFTSql,
		arg.OwnerAddress,
		arg.BlockNumber,
		arg.BlockTimestamp,
		arg.TokenId,
	)
	if err != nil {
		return
	}
	updated = tag.RowsAffected() > 0
	return
}

const insertGenesisNFTSql = `insert into tb_genesis_nft
	(token_id, owner_address, material, block_number, block_timestamp, created_at, updated_at) values 
	($1, $2, $3, $4, $5, (now() at time zone 'utc'), (now() at time zone 'utc'))`

func InsertGenesisNFT(arg *model.InsertGenesisNFTArg) (err error) {
	_, err = postgres.ExecByRW(
		insertGenesisNFTSql,
		arg.TokenId,
		arg.OwnerAddress,
		arg.Material,
		arg.BlockNumber,
		arg.BlockTimestamp,
	)
	return
}

const queryUserGenesisNFTStatSql = `select material, coalesce(count(1), 0) as material_count, array_agg(token_id) as token_ids 
from tb_genesis_nft where owner_address = $1 group by material`

func QueryUserGenesisNFTStat(userAddress string) (stat *model.UserGenesisNFTStat, err error) {
	var records []model.UserGenesisNFTStatRecord
	err = postgres.SelectByRW(queryUserGenesisNFTStatSql, &records, userAddress)
	if err != nil {
		return
	}
	stat = &model.UserGenesisNFTStat{}
	for _, r := range records {
		switch r.Material {
		case model.GenesisNFTMaterialGold:
			stat.GoldCount = r.MaterialCount
			stat.GoldTokenIds = r.TokenIds
		case model.GenesisNFTMaterialSilver:
			stat.SilverCount = r.MaterialCount
			stat.SilverTokenIds = r.TokenIds
		case model.GenesisNFTMaterialBronze:
			stat.BronzeCount = r.MaterialCount
			stat.BronzeTokenIds = r.TokenIds
		}
	}
	return
}
