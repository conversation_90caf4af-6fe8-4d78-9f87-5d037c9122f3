package env

import (
	"os"
	"testing"
	"time"
)

type tNormalWithoutDefault struct {
	String   string        `env:"STRING_TST"`
	Int      int           `env:"INT_TST"`
	Bool     bool          `env:"BOOL_TST"`
	Duration time.Duration `env:"DURATION_TST"`
	Array    []string      `env:"ARRAY_TST"`
}

type tNormalWithDefault struct {
	String   string        `env:"STRING_TST" default:"string"`
	Int      int           `env:"INT_TST" default:"1024"`
	Bool     bool          `env:"BOOL_TST" default:"true"`
	Duration time.Duration `env:"DURATION_TST" default:"1h"`
	Array    []string      `env:"ARRAY_TST" default:"a,b"`
}

type tInvalidDefaultInt struct {
	Int int `env:"INT_TST" default:"abc"`
}

type tInvalidDefaultBool struct {
	Bool bool `env:"BOOL_TST" default:"abc"`
}

type tInvalidDefaultDuration struct {
	Duration time.Duration `env:"DURATION_TST" default:"abc"`
}

type tOmitted struct {
	String   string        `env:",omitted"`
	Int      int           `env:",omitted"`
	Bool     bool          `env:",omitted"`
	Duration time.Duration `env:",omitted"`
	Array    []string      `env:",omitted"`
}

type tMust struct {
	String   string        `env:"STRING_TST,must"`
	Int      int           `env:"INT_TST,must"`
	Bool     bool          `env:"BOOL_TST,must"`
	Duration time.Duration `env:"DURATION_TST,must"`
	Array    []string      `env:"ARRAY_TST,must"`
}

func TestScanStruct(t *testing.T) {
	var (
		stringSet   = "string"
		stringGet   = "string"
		intSet      = "1024"
		intGet      = 1024
		boolSet     = "true"
		boolGet     = true
		durationSet = "1h"
		durationGet = 1 * time.Hour
		arraySet    = "a,b"
		arrayGet    = []string{"a", "b"}
	)

	t.Log("-- normal with default")
	var nd tNormalWithDefault
	ScanTo(&nd)
	t.Logf("%+v", nd)
	if nd.String != stringGet {
		t.Error("string failed")
	}
	if nd.Int != intGet {
		t.Error("int failed")
	}
	if nd.Bool != boolGet {
		t.Error("bool failed")
	}
	if nd.Duration != durationGet {
		t.Error("duration failed")
	}
	if nd.Array[0] != arrayGet[0] ||
		nd.Array[1] != arrayGet[1] {
		t.Error("array failed")
	}
	os.Setenv("INT_TST", "abc")
	os.Setenv("BOOL_TST", "abc")
	os.Setenv("DURATION_TST", "abc")
	ScanTo(&nd)
	if nd.Int != intGet {
		t.Error("int failed")
	}
	if nd.Bool != boolGet {
		t.Error("bool failed")
	}
	if nd.Duration != durationGet {
		t.Error("duration failed")
	}

	t.Log("-- normal without default")
	var nnd tNormalWithoutDefault
	ScanTo(&nnd)
	t.Logf("%+v", nnd)
	if nnd.String == stringGet {
		t.Error("string failed")
	}
	if nnd.Int == intGet {
		t.Error("int failed")
	}
	if nnd.Bool == boolGet {
		t.Error("bool failed")
	}
	if nnd.Duration == durationGet {
		t.Error("duration failed")
	}
	if len(nnd.Array) > 0 {
		t.Error("array failed")
	}

	t.Log("-- invalid default")
	func() {
		var indInt tInvalidDefaultInt
		defer func() {
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("int failed")
			}
		}()
		ScanTo(&indInt)
	}()
	func() {
		var indBool tInvalidDefaultBool
		defer func() {
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("bool failed")
			}
		}()
		ScanTo(indBool)
	}()
	func() {
		var indDuration tInvalidDefaultDuration
		defer func() {
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("duration failed")
			}
		}()
		ScanTo(indDuration)
	}()

	t.Log("-- set env")
	os.Setenv("STRING_TST", stringSet)
	os.Setenv("INT_TST", intSet)
	os.Setenv("BOOL_TST", boolSet)
	os.Setenv("DURATION_TST", durationSet)
	os.Setenv("ARRAY_TST", arraySet)

	t.Log("-- normal without default")
	ScanTo(&nnd)
	t.Logf("%+v", nnd)
	if nnd.String != stringGet {
		t.Error("string failed")
	}
	if nnd.Int != intGet {
		t.Error("int failed")
	}
	if nnd.Bool != boolGet {
		t.Error("bool failed")
	}
	if nnd.Duration != durationGet {
		t.Error("duration failed")
	}
	if nnd.Array[0] != arrayGet[0] ||
		nnd.Array[1] != arrayGet[1] {
		t.Error("array failed")
	}

	t.Log("-- omitted")
	var omt tOmitted
	ScanTo(&omt)
	t.Logf("%+v", omt)
	if omt.String == stringGet {
		t.Error("string failed")
	}
	if omt.Int == intGet {
		t.Error("int failed")
	}
	if omt.Bool == boolGet {
		t.Error("bool failed")
	}
	if omt.Duration == durationGet {
		t.Error("duration failed")
	}
	if len(omt.Array) > 0 {
		t.Error("array failed")
	}

	t.Log("-- must")
	var must tMust
	func() {
		os.Unsetenv("STRING_TST")
		defer func() {
			os.Setenv("STRING_TST", stringSet)
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("string failed")
			}
		}()
		ScanTo(&must)
	}()
	func() {
		os.Setenv("INT_TST", "abc")
		defer func() {
			os.Setenv("INT_TST", intSet)
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("int failed")
			}
		}()
		ScanTo(&must)
	}()
	func() {
		os.Setenv("BOOL_TST", "abc")
		defer func() {
			os.Setenv("BOOL_TST", boolSet)
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("bool failed")
			}
		}()
		ScanTo(&must)
	}()
	func() {
		os.Setenv("DURATION_TST", "abc")
		defer func() {
			os.Setenv("DURATION_TST", durationSet)
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("duration failed")
			}
		}()
		ScanTo(&must)
	}()
	func() {
		os.Unsetenv("ARRAY_TST")
		defer func() {
			os.Setenv("ARRAY_TST", arraySet)
			r := recover()
			t.Log(r)
			if r == nil {
				t.Error("duration failed")
			}
		}()
		ScanTo(&must)
	}()
}
