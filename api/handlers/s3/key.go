package s3

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/api/cctx"
	"gitlab.insidebt.net/btfs/storage3-backend/api/response"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/services/s3"
)

// s3 key manage

func GenerateKey(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	key, err := s3.GenerateKey(ctx.Request.Context(), user.UserId)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	data := response.Data{}
	err = json.Unmarshal(key, &data)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	response.DataOK(ctx, data)
}

func EnableKey(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()

	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	req := &model.ActiveKeyReq{}
	err = ctx.ShouldBind(req)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	_, err = s3.ManageKey(context.Background(), req, user.UserId, s3.Enable)
	if err != nil {
		errProcess(ctx, err)
		return
	}
	response.OK(ctx)
}

func DisableKey(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	req := &model.ActiveKeyReq{}
	err = ctx.ShouldBind(req)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	_, err = s3.ManageKey(ctx.Request.Context(), req, user.UserId, s3.Disable)
	if err != nil {
		errProcess(ctx, err)
		return
	}
	response.OK(ctx)
}

func ResetKey(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	req := &model.ActiveKeyReq{}
	err = ctx.ShouldBind(req)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	_, err = s3.ManageKey(context.Background(), req, user.UserId, s3.Reset)
	if err != nil {
		errProcess(ctx, err)
		return
	}
	response.OK(ctx)
}

func DeleteKey(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	req := &model.ActiveKeyReq{}
	err = ctx.ShouldBind(req)
	if err != nil {
		response.ParamErr(ctx, err)
		return
	}

	_, err = s3.ManageKey(context.Background(), req, user.UserId, s3.Delete)
	if err != nil {
		errProcess(ctx, err)
		return
	}
	response.OK(ctx)
}

func ListKeys(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	res, err := s3.ListKey(context.Background(), user.UserId)
	if err != nil {
		response.InternalErr(ctx)
		return
	}
	response.DataOK(ctx, response.Data{
		"list":  res,
		"total": len(res),
	})
}

func DetailKey(ctx *gin.Context) {
	var err error
	defer func() {
		cctx.SetErr(ctx, err)
	}()
	user, err := cctx.GetLoginUser(ctx)
	if err != nil {
		response.InternalErr(ctx)
		return
	}

	req := &model.DetailKeyReq{}
	err = ctx.ShouldBind(req)

	key, err := s3.DetailKey(context.Background(), req.Key, user.UserId)

	if err != nil {
		errProcess(ctx, err)
		return
	}

	response.DataOK(ctx, response.Data{
		"detail": key,
	})
}

func errProcess(ctx *gin.Context, err error) {
	if err == nil {
		return
	}
	if errors.Is(err, s3.ErrorKeyNotFound) {
		response.BadRequestErr(ctx, response.CodeKeyError, err)
		return
	}

	if errors.Is(err, s3.ErrorKeyNotMatchUserId) {
		response.BadRequestErr(ctx, response.CodeKeyError, err)
		return
	}

	response.InternalErr(ctx)
}
