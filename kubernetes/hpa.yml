---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: $SERVICE_NAME-hpa
  namespace: $NAMESPACE
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: $SERVICE_NAME
  minReplicas: $MIN_PODS
  maxReplicas: $MAX_PODS
  targetCPUUtilizationPercentage: 50
---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: $SERVICE_NAME-jobs-hpa
  namespace: $NAMESPACE
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: $SERVICE_NAME-jobs
  minReplicas: $MIN_PODS_JOB
  maxReplicas: $MAX_PODS_JOB
  targetCPUUtilizationPercentage: 50