create table if not exists tb_site_transfer_record
(
    id                          bigserial primary key,
    site_id                     character varying(255)      NOT NULL default ''::character varying,
    from_user_id                character varying(128)      NOT NULL default ''::character varying,
    from_address                character varying(255) NOT NULL default ''::character varying,
    from_signature              character varying(255) NOT NULL default ''::character varying,
    from_address_type           character varying(16) NOT NULL default ''::character varying,
    from_signature_message      character varying(2048) not null default ''::character varying,
    to_user_id                  character varying(128)      NOT NULL default ''::character varying,
    to_address                  character varying(255) NOT NULL default ''::character varying,
    to_address_type             character varying(16) NOT NULL default ''::character varying,
    to_signature                character varying(255) NOT NULL default ''::character varying,
    to_signature_message        character varying(2048) NOT NULL default ''::character varying,
    transfer_status             INT NOT NULL default '1'::int,  -- 1: pending, 2: accept, 3: decline
    created_at                 timestamp without time zone not null default (now() at time zone 'utc'),
    updated_at                 timestamp without time zone not null default (now() at time zone 'utc')
);
create index tb_site_transfer_record_site_id_idx on tb_site_transfer_record(site_id);
