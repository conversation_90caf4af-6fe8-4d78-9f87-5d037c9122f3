package redis

import (
	"gitlab.insidebt.net/btfs/storage3-backend/clean"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var svc Service

func init() {
	var err error
	svc, err = newService(&serviceOptions{
		Network:         config.Redis.Network,
		Address:         config.Redis.Host,
		Password:        config.Redis.Password,
		DB:              config.Redis.DB,
		MaxIdle:         config.Redis.MaxIdleConn,
		MaxActive:       config.Redis.MaxActiveConn,
		IdleTimeout:     config.Redis.ConnIdleTimeout,
		Wait:            config.Redis.ConnWait,
		MaxConnLifetime: config.Redis.MaxConnLifetime,
	})
	if err != nil {
		panic(err)
	}
	clean.PushClose(svc.Close)
}

func SetNxEx(key, val string, timeoutSecs int) (ok bool, err error) {
	return svc.SetNxEx(key, val, timeoutSecs)
}

func Del(key string) (n int64, err error) {
	return svc.Del(key)
}

func Expire(key string, timeoutSecs int) (ok bool, err error) {
	return svc.Expire(key, timeoutSecs)
}

func Lock(key string, retries, timeoutSecs int) (unlock func() error, err error) {
	return svc.Lock(key, retries, timeoutSecs)
}
