package middlewares

import (
	"strings"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/redis"
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var sessStore sessions.Store

const (
	sessionKey    = "xSess"
	sessionSecret = "xSessSecret"
)

func init() {
	var err error
	
	// Check if we have multiple Redis addresses (cluster mode)
	redisAddr := config.Redis.Host
	if strings.Contains(redisAddr, ",") {
		// For cluster mode, we pass all addresses as a single comma-separated string
		// The updated redis store in gin-contrib/sessions v1.0.4 handles this properly
		sessStore, err = redis.NewStore(
			config.Redis.MaxIdleConn,
			config.Redis.Network,
			redisAddr,
			"", // username not used
			config.Redis.Password,
			[]byte(sessionSecret),
		)
	} else {
		// Single instance mode
		sessStore, err = redis.NewStore(
			config.Redis.MaxIdleConn,
			config.Redis.Network,
			config.Redis.Host,
			"", // username not used
			config.Redis.Password,
			[]byte(sessionSecret),
		)
	}
	
	if err != nil {
		panic(err)
	}
}

func Session() gin.HandlerFunc {
	return sessions.Sessions(sessionKey, sessStore)
}