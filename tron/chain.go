package tron

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"sync"
	"time"
)

type chain struct {
	client     *http.Client
	host       string
	key        string
	intervalMs int64
	prevMs     int64
	interLock  *sync.Mutex
}

func new<PERSON><PERSON>n(host, key string, intervalMs int64) (s *chain) {
	s = &chain{
		client:     http.DefaultClient,
		host:       host,
		key:        key,
		intervalMs: intervalMs,
		prevMs:     time.Now().UnixMilli(),
		interLock:  &sync.Mutex{},
	}
	return
}

func (c *chain) buildRequest(ctx context.Context, path string, params url.Values, body io.Reader) (req *http.Request, err error) {
	uri, err := url.ParseRequestURI(c.host)
	if err != nil {
		return
	}
	uri.Path = filepath.Clean(path)
	uri.RawQuery = params.Encode()
	fmt.Println(uri.String())
	req, err = http.NewRequestWithContext(
		ctx, "GET",
		uri.String(), body,
	)
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("TRON-PRO-API-KEY", c.key)
	return
}

func (c *chain) doRequest(req *http.Request, rsp interface{}) (err error) {
	c.interLock.Lock()
	nowMs := time.Now().UnixMilli()
	pasMs := nowMs - c.prevMs
	if !(pasMs > c.intervalMs) {
		time.Sleep(time.Duration(pasMs) * time.Millisecond)
	}
	c.prevMs = nowMs
	c.interLock.Unlock()

	resp, err := c.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	err = json.NewDecoder(resp.Body).Decode(rsp)
	return
}

// GetEventsByContract reference: https://developers.tron.network/reference/get-events-by-contract-address

func (c *chain) buildGetEventByContractRequest(ctx context.Context, arg *GetEventByContractArg) (req *http.Request, err error) {
	path := fmt.Sprintf("v1/contracts/%s/events", arg.Address)
	params := make(url.Values)
	if arg.EventName != "" {
		params.Set("event_name", arg.EventName)
	}
	if arg.BlockNumber > 0 {
		params.Set("block_number", fmt.Sprintf("%d", arg.BlockNumber))
	}
	if arg.OnlyUnConfirmed {
		params.Set("only_unconfirmed", "true")
	}
	if arg.OnlyUnConfirmed {
		params.Set("only_confirmed", "true")
	}
	if arg.MinBlockTimestamp > 0 {
		params.Set("min_block_timestamp", fmt.Sprintf("%d", arg.MinBlockTimestamp))
	}
	if arg.MaxBlockTimestamp > 0 {
		params.Set("max_block_timestamp", fmt.Sprintf("%d", arg.MaxBlockTimestamp))
	}
	if arg.OrderBy != "" {
		params.Set("order_by", arg.OrderBy)
	}
	if arg.Fingerprint != "" {
		params.Set("fingerprint", arg.Fingerprint)
	}
	if arg.Limit > 0 {
		params.Set("limit", fmt.Sprintf("%d", arg.Limit))
	}
	req, err = c.buildRequest(ctx, path, params, nil)
	return
}

func (c *chain) GetEventByContract(ctx context.Context, arg *GetEventByContractArg) (rsp *GetEventByContractResponseBody, err error) {
	req, err := c.buildGetEventByContractRequest(ctx, arg)
	if err != nil {
		return
	}
	rsp = &GetEventByContractResponseBody{}
	err = c.doRequest(req, rsp)
	return
}

// TriggerConstantContract reference: https://developers.tron.network/reference/triggerconstantcontract

func (c *chain) buildTriggerConstantContractRequest(ctx context.Context, arg *TriggerConstantContractArg) (req *http.Request, err error) {
	path := "wallet/triggerconstantcontract"
	buf := &bytes.Buffer{}
	err = json.NewEncoder(buf).Encode(arg)
	if err != nil {
		return
	}
	req, err = c.buildRequest(ctx, path, nil, buf)
	if err != nil {
		return
	}
	req.Method = "POST"
	req.Header.Set("Accept", "application/json")
	return
}

func (c *chain) TriggerConstantContract(ctx context.Context, arg *TriggerConstantContractArg) (rsp *TriggerConstantContractResponseBody, err error) {
	req, err := c.buildTriggerConstantContractRequest(ctx, arg)
	if err != nil {
		return
	}
	rsp = &TriggerConstantContractResponseBody{}
	err = c.doRequest(req, rsp)
	return
}
