package visitstat

import (
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/redis"
	"golang.org/x/sync/errgroup"
	"time"
)

const (
	domainHistoryLimit = 1000
	routineLimit       = 20
	jobKey             = "storage3_visit_stat_job"
)

func DoDailyStat() (err error) {
	defer func() {
		log.WithFields(log.Fields{
			"module": "site_visit_daily_stat",
		}).IfErr(err)
	}()

	unlock, err := redis.Lock(jobKey, 0, 60*60)
	if err != nil {
		return
	}
	defer unlock()

	today := time.Now().Truncate(24 * time.Hour)
	startDate, err := data.GetLastSiteVisitStatDate()
	if err != nil {
		return
	}

	for dateFrom := startDate; dateFrom.Before(today); dateFrom = dateFrom.Add(24 * time.Hour) {
		dateTo := dateFrom.Add(24 * time.Hour)
		var (
			list   []*model.SiteDomainHistory
			lastId int64
		)
		for {
			list, err = data.GetSiteDomainHistoryList(dateFrom, dateTo, lastId, domainHistoryLimit)
			if err != nil {
				return
			}
			if len(list) == 0 {
				continue
			}
			eg := errgroup.Group{}
			eg.SetLimit(routineLimit)
			for _, history := range list {
				his := history
				eg.Go(func() (err error) {
					var stat model.SiteVisitStat
					defer func() {
						log.WithFields(log.Fields{
							"module": "site_visit_stat_per_domain",
							"stat":   stat,
						}).IfErr(err)
					}()
					var domainStat *model.DomainVisitStat
					vFrom := laterTime(his.FromTime, dateFrom)
					vTo := olderTime(his.ToTime, dateTo)
					domainStat, err = data.GetDomainVisitStatFromGatewayLogs(
						his.Domain,
						dateFrom,
						vFrom,
						vTo,
					)
					if err != nil {
						return
					}
					stat = model.SiteVisitStat{
						SiteId:    his.SiteId,
						Domain:    his.Domain,
						PV:        domainStat.PV,
						UV:        domainStat.UV,
						Bandwidth: domainStat.Bandwidth,
						StatDate:  dateFrom,
						FromTime:  vFrom,
						ToTime:    vTo,
					}
					err = data.InsertSiteVisitStatRecord(&stat)
					return
				})
			}
			err = eg.Wait()
			if err != nil {
				return
			}
			lastId = list[len(list)-1].Id
			if len(list) < domainHistoryLimit {
				break
			}
		}
	}
	return
}

func laterTime(a, b time.Time) time.Time {
	if a.Before(b) {
		return b
	}
	return a
}

func olderTime(a, b time.Time) time.Time {
	if a.After(b) {
		return b
	}
	return a
}
