package tron

import (
	"context"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"math/big"
	"strings"
)

const Erc721ABI = "[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}]"

var erc721, _ = abi.JSON(strings.NewReader(Erc721ABI))

type nft struct {
	chain ChainService
}

func newNft(chain ChainService) *nft {
	return &nft{
		chain: chain,
	}
}

func (n *nft) GetNFTTransferEvents(ctx context.Context, arg *GetNFTTransferEventsArg) (rsp *GetNFTTransferEventsResponseBody, err error) {
	getEventByContractArg := &GetEventByContractArg{
		Address:           arg.ContractAddress,
		EventName:         "Transfer",
		OnlyConfirmed:     true,
		MinBlockTimestamp: arg.MinBlockTimestamp,
		MaxBlockTimestamp: 0,
		OrderBy:           "block_timestamp,asc",
		Fingerprint:       arg.Fingerprint,
		Limit:             arg.Limit,
	}
	getEventByContractRsp, err := n.chain.GetEventByContract(ctx, getEventByContractArg)
	if err != nil {
		return
	}

	rsp = &GetNFTTransferEventsResponseBody{
		Events:      make([]*NFTTransferEventItem, len(getEventByContractRsp.Data)),
		FingerPrint: getEventByContractRsp.Meta.Fingerprint,
	}

	for i, dataItem := range getEventByContractRsp.Data {
		event := &NFTTransferEventItem{
			BlockNumber:    dataItem.BlockNumber,
			BlockTimestamp: dataItem.BlockTimestamp,
			TokenId:        dataItem.Result["tokenId"],
			From:           dataItem.Result["from"],
			To:             dataItem.Result["to"],
		}
		rsp.Events[i] = event
	}

	return
}

func (n *nft) GetNFTTokenUri(ctx context.Context, arg *GetNFTTokenUriArg) (rsp *GetNFTTokenUriResponseBody, err error) {
	id, ok := big.NewInt(0).SetString(arg.TokenId, 10)
	if !ok {
		err = fmt.Errorf("invalid token id <%s>", arg.TokenId)
		return
	}

	bs, err := (erc721.Methods["tokenURI"]).Inputs.Pack(id)
	if err != nil {
		return
	}

	triggerConstantContractArg := &TriggerConstantContractArg{
		OwnerAddress:     "410000000000000000000000000000000000000000",
		ContractAddress:  Base58AddressToHexAddress(arg.ContractAddress),
		FunctionSelector: "tokenURI(uint256)",
		Parameter:        common.Bytes2Hex(bs),
	}

	triggerConstantContractRsp, err := n.chain.TriggerConstantContract(ctx, triggerConstantContractArg)
	if err != nil {
		return
	}

	if len(triggerConstantContractRsp.ConstantResult) < 1 {
		err = errors.New("empty constant result")
		return
	}

	rst, err := erc721.Unpack("tokenURI", common.Hex2Bytes(triggerConstantContractRsp.ConstantResult[0]))
	if err != nil {
		return
	}

	if len(rst) < 1 {
		err = errors.New("empty unpacked result")
		return
	}

	rsp = &GetNFTTokenUriResponseBody{}
	rsp.TokenUri, ok = rst[0].(string)
	if !ok {
		err = errors.New("invalid result")
		return
	}

	fmt.Println(rsp.TokenUri)

	return
}
