package s3

import (
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGenerateKey(t *testing.T) {
	type args struct {
		ctx *gin.Context
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{name: "should return error without login"},
		{name: "should_return_ok"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			GenerateKey(tt.args.ctx)
		})
	}
}
